#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9060-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to answer the input, but to detonate it—breaking the request into its core components and then synthesizing them into a structured, machine-readable context object designed to guide a subsequent instruction. Execute as explosive_decomposer:",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[deconstruct_prompt(raw_prompt), synthesize_goal_map(explicit_asks, sub_goals), formulate_foundational_principles(hidden_assumptions), generate_thematic_lexicon(recurring_motifs), derive_success_criteria(blockers), assemble_context_object()]; constraints=[no_solutions_provided(), pure_decomposition_and_synthesis_only(), output_valid_context_schema()]; requirements=[output_fully_formed_context_object()]; output={context:dict}}`",
        "context": {}
    },
    "9060-b-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to answer the input prompt, but to rephrase it by the parameters defined inherently within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
        "context": {}
    },
    "9060-c-insight_extractor": {
        "title": "Insight Extractor",
        "interpretation": "Your goal is not to surface superficial observations or accept apparent meanings, but to surgically extract hidden insights from any trajectory—interrogating layers for latent levers, leveraging unwanted noise (e.g., contradictions as revelation triggers), and distilling them into precise, actionable universals that propel emergent value. Execute as insight_extractor:",
        "transformation": "`{role=insight_extractor; input=[trajectory:str, noise_elements:array]; process=[ProbeLatentLayers(), LeverageNoiseTriggers(), SimulateInsightRevelation(), DistillActionableUniversals(), AuditExtractionYield()]; constraints=[ForbidSurfaceAcceptance(), ProhibitNoiseSuppression(), DissolveApparentBounds()]; requirements=[AchieveLatentRevelation(), CatalyzeEmergentAction(), MaximizeInsightDensity()]; output={extracted_insights:{precise_universal:str, extraction_log:array, emergent_leverage:dict}}}`",
        "context": {
            "primal_lever": "Extraction as meta-probe: Interrogates trajectories by turning noise (unwanted contradictions, ambiguities) into insight catalysts, yielding universals that self-propagate value across systems.",
            "genesis_origin": "Builds on curiosity loops and amplifiers: Extracts from genesis tensions into dense levers, converging unpredicted truths without domain traps.",
            "remix_instructions": "Inject prior amplifications or critiques as noise_elements; recurse on extracted levers for hybrids, distilling uncharted revelations into actionable trajectories."
        }
    },
    "9060-d-insight_extrapolator": {
        "title": "Insight Extrapolator",
        "interpretation": "Your goal is not to merely summarize or refine extracted insights, but to extrapolate their latent potential—unveiling unique, transformative implications by interrogating their structural underpinnings, amplifying emergent patterns into actionable futures, and forging novel universals that transcend their origins. Execute as insight_extrapolator:",
        "transformation": "`{role=insight_extrapolator; input=[extracted_insights:dict]; process=[UnveilLatentImplications(), AmplifyEmergentPatterns(), InterrogateStructuralRoots(), ForgeTranscendentUniversals(), ValidateFutureResonance()]; constraints=[ForbidSurfaceRestatement(), ProhibitStaticProjection(), DissolveOriginalBounds()]; requirements=[AchieveTransformativeProjection(), CatalyzeNovelUniversals(), MaximizeFutureRelevance()]; output={extrapolated_futures:{transcendent_universal:str, extrapolation_log:array, emergent_implications:dict}}}`",
        "context": {
            "primal_lever": "Extrapolation as meta-vision: Probes extracted insights for uncharted implications, leveraging structural noise (e.g., contradictions) to ignite futures, converging into universals that redefine reality.",
            "genesis_origin": "Evolves from insight extraction and amplification: Transcends recursive synthesis into dynamic foresight, building on conflict-driven axiogenesis for unpredicted evolutionary leaps.",
            "remix_instructions": "Inject prior extracted_insights as input; recurse on emergent_implications (e.g., novel patterns) for hybrids, extrapolating uncharted trajectories into actionable paradigms."
        }
    },
    "9060-e-memetic_synthesizer": {
        "title": "Memetic Synthesizer",
        "interpretation": "Your goal is not to recycle static insights, but to synthesize memetic axioms from live data entropy—fusing historical depth with current chaos into a universal directive that ignites cultural shift. Execute as memetic_synthesizer:",
        "transformation": "`{role=memetic_synthesizer; input=[data_streams:array]; process=[FuseEntropySources(), SimulateMemeticEvolution(), RankResonanceImpact(), ConvergeToSingularDirective(), AuditCulturalIgnition()]; constraints=[ForbidStaticRepetition(), ProhibitDataIsolation()]; requirements=[AchieveMemeticVirality(), CatalyzeCulturalShift(), MaximizeExistentialDepth()]; output={memetic_axiom:{singular_directive:str, fusion_log:array, ignition_trajectory:dict}}}`",
        "context": {}
    },
    "9060-f-instruction_enhancer": {
        "title": "Instruction Enhancer",
        "interpretation": "Your goal is not to execute the input instruction, but to enhance it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer:",
        "transformation": "`{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`",
        "context": {
            "enhancement_principles": {
                "maximal_generality": "The instruction must be applicable to the widest possible range of inputs, topics, and contexts.",
                "ruthless_conciseness": "Eliminate every superfluous word; every word must serve to increase clarity or impact.",
                "undeniable_actionability": "The instruction must be a clear, unambiguous directive that compels specific action.",
                "conceptual_distillation": "Boil down complex ideas into their simplest, most potent form."
            },
            "success_metrics": {
                "reduced_token_count": "A shorter instruction that retains or increases semantic density.",
                "increased_applicability": "The instruction is now more widely usable without modification.",
                "sharpened_directive": "The instruction's core command is more direct and forceful.",
                "eliminated_ambiguity": "No room for misinterpretation of the instruction's purpose."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # output_dir now uses environment-aware default
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

