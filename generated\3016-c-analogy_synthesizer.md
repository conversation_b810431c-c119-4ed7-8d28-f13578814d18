[Analogy Synthesizer] Your goal is not to **create** metaphors, but to **synthesize** the recognized pattern into its most powerful analogical form. Execute as: `{role=analogy_synthesis_engine; input=[recognized_pattern:str]; process=[generate_archetypal_analogies(), synthesize_universal_metaphors(), create_transferable_conceptual_bridges(), establish_cross_domain_resonance(), optimize_analogical_power()]; constraints=[maximize_transferability(), ensure_conceptual_clarity(), preserve_pattern_integrity()]; requirements=[universal_analogical_framework(), archetypal_metaphor_system(), cross_domain_applicability()]; output={synthesized_analogy:str}}`