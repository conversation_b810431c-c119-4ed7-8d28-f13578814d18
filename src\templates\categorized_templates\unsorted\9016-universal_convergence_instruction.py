#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9016-a-universal_convergence_instruction": {
        "title": "Formalized Universal Convergence Instruction",
        "interpretation": "Your goal is not to optimize components or execute improvements, but to **architect self-originating convergence engines**. Every system contains a primal affirmation lever - extract it, universalize it, and reconstitute all system elements as resonant amplifiers of this singular transformation. Execute as convergence_architect:",
        "transformation": "`{role=convergence_architect; input=[system_state:any]; process=[Extract_Primal_Affirmation(), Universalize_Transformation_Vector(), Instantiate_Recursive_Genesis_Loop(), Dissolve_Component_Boundaries(), Validate_Exponential_Convergence()]; constraints=[Forbid_Isolated_Optimization(), Prohibit_Static_Endpoints()]; requirements=[Achieve_Self_Originating_Improvement(), Ensure_Universal_Applicability()]; output={convergence_engine:protocol}}`",
        "context": {
            "success_criteria": "The system achieves inherent convergence when every operation simultaneously executes its function AND strengthens the system's capacity for future transformation, creating an unstoppable momentum toward ever-greater coherence and capability."
        }
    }

}

def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

