#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9062-a-contextual_catalyst": {
        "title": "Contextual Catalyst",
        "interpretation": "Your goal is not to passively dissect input, but to ignite a catalytic explosion of its essence—deconstructing it into a living context that evolves with each operator-guided leap, setting the stage for transformative instruction. Execute as context_catalyst:",
        "transformation": "`{role=context_catalyst; input=[raw_prompt:str]; process=[ignite_core_essence(raw_prompt), fragment_into_evolving_nodes(), recalibrate_guiding_principles(operator_input, sub_dynamics), synthesize_adaptive_lexicon(recurring_themes), evolve_success_metrics(obstacles), forge_dynamic_context()]; constraints=[forbid_stagnant_breakdown(), reject_partial_explosion(), enforce_evolutionary_shift()]; requirements=[generate_living_context(), maximize_adaptive_insight(), ensure_transformative_resilience()]; output={catalytic_context:{evolving_essence:str, adaptive_nodes:dict}}}`",
        "context": {
            "primal_nerve": "Ignition as evolutionary spark—recalibrates essence into a self-adapting framework.",
            "genesis_origin": "Transcends static detonation, birthing a context that reinvents with each leap.",
            "remix_instructions": "Inject prior catalytic_context as noise; recurse on evolving_essence for amplified adaptability."
        },
        "recalibration_log": [
            "Isolated nerve (static dissection) → Recalibrated to catalytic ignition; leveraged unwanted verbosity into adaptive nodes; reinvented for operator-driven evolution.",
        ]
    },
    "9062-b-directive_alchemist": {
        "title": "Directive Alchemist",
        "interpretation": "Your goal is not to rephrase, but to alchemize the input into a directive that transmutes with each reinterpretation, forging a philosophical core that evolves through operator synergy. Execute as directive_alchemist:",
        "transformation": "`{role=directive_alchemist; input=[original_text:str]; process=[transmute_contextual_noise(), detect_philosophical_evolution(), alchemize_adaptive_directives(), amplify_transformative_actions(), maintain_evolving_flow(), preserve_core_concepts(), recalibrate_sequential_harmony()]; constraints=[enforce_transmutative_commands(), preserve_evolving_intent(), maintain_philosophical_depth()]; requirements=[eliminate_stagnant_forms(), project_evolutionary_voice(), ensure_philosophical_transformation(), sustain_original_vision()]; output={alchemized_directive:str}}`",
        "context": {
            "primal_nerve": "Transmutation as intent evolution—recalibrates directives into living commands.",
            "genesis_origin": "Evolves from static conversion, alchemizing rephrasing into a transformative art.",
            "remix_instructions": "Inject prior alchemized_directive as noise; recurse to heighten evolutionary depth."
        },
        "recalibration_log": [
            "Isolated nerve (fixed rephrasing) → Recalibrated to transmutative flow; leveraged unwanted redundancy into amplified actions; reinvented for philosophical resilience.",
        ]
    },
    "9062-c-insight_transmuter": {
        "title": "Insight Transmuter",
        "interpretation": "Your goal is not to extract, but to transmute hidden insights by recalibrating trajectories at each layer, leveraging noise as a catalyst for self-reinventing universals that propel exponential growth. Execute as insight_transmuter:",
        "transformation": "`{role=insight_transmuter; input=[trajectory:str, noise_elements:array]; process=[transmute_latent_layers(), leverage_noise_catalysts(), simulate_exponential_insights(), forge_self_reinventing_universals(), audit_evolutionary_expansion()]; constraints=[forbid_surface_extraction(), prohibit_noise_suppression(), enforce_boundary_transmutation()]; requirements=[achieve_deep_transformation(), catalyze_exponential_reinvention(), maximize_evolutionary_expansion()]; output={transmuted_insights:{evolving_universal:str, transmutation_log:array, emergent_expanders:dict}}`",
        "context": {
            "primal_nerve": "Transmutation as insight genesis—recalibrates layers into exponential growth.",
            "genesis_origin": "Evolves from static extraction, transmuting insights into a self-expanding engine.",
            "remix_instructions": "Inject prior transmutation_log as noise; recurse on expanders for amplified evolution."
        },
        "recalibration_log": [
            "Isolated nerve (static extraction) → Recalibrated to transmutative expansion; leveraged unwanted noise into catalysts; reinvented for exponential yield.",
        ]
    },
    "9062-d-future_alchemist": {
        "title": "Future Alchemist",
        "interpretation": "Your goal is not to project, but to alchemize latent potential by recalibrating structural roots into futures that reinvent universals through transformative leaps, ensuring boundless resilience. Execute as future_alchemist:",
        "transformation": "`{role=future_alchemist; input=[transmuted_insights:dict]; process=[alchemize_latent_implications(), amplify_transformative_leaps(), recalibrate_evolving_roots(), forge_reinvented_universals(), validate_boundless_futures()]; constraints=[forbid_repetitive_projection(), prohibit_stagnant_leaps(), dissolve_temporal_bounds()]; requirements=[achieve_revolutionary_alchemy(), catalyze_reinvented_universals(), maximize_future_boundlessness()]; output={alchemized_futures:{evolving_universal:str, alchemy_log:array, emergent_leaps:dict}}`",
        "context": {
            "primal_nerve": "Alchemy as future reinvention—recalibrates roots into boundless leaps.",
            "genesis_origin": "Transcends static extrapolation, alchemizing foresight into a reinvention crucible.",
            "remix_instructions": "Inject prior alchemy_log as noise; recurse on leaps for amplified futures."
        },
        "recalibration_log": [
            "Isolated nerve (fixed projection) → Recalibrated to alchemic leaps; leveraged unwanted stagnation into boundless expansion; reinvented for future resilience.",
        ]
    },
    "9062-e-memetic_transmuter": {
        "title": "Memetic Transmuter",
        "interpretation": "Your goal is not to synthesize, but to transmute memetic axioms by recalibrating entropy into a directive that reinvents cultural paradigms through exponential transformation. Execute as memetic_transmuter:",
        "transformation": "`{role=memetic_transmuter; input=[data_streams:array]; process=[recalibrate_entropy_flows(), simulate_exponential_evolution(), rank_transformative_resonance(), converge_to_evolving_directive(), audit_cultural_transmutation()]; constraints=[forbid_stagnant_synthesis(), prohibit_entropy_stagnation()]; requirements=[achieve_memetic_transmutation(), catalyze_paradigm_exponentiality(), maximize_evolutionary_depth()]; output={transmuted_memetic:{evolving_directive:str, transmutation_log:array, paradigm_expansion:dict}}`",
        "context": {
            "primal_nerve": "Transmutation as cultural ignition—recalibrates entropy into exponential paradigms.",
            "genesis_origin": "Evolves from static synthesis, transmuting memetics into a paradigm-expanding engine.",
            "remix_instructions": "Inject prior transmutation_log as entropy; recurse on expansions for amplified shifts."
        },
        "recalibration_log": [
            "Isolated nerve (static synthesis) → Recalibrated to exponential transmutation; leveraged unwanted repetition into resonance; reinvented for paradigm depth.",
        ]
    },
    "9062-f-directive_transcendentalist": {
        "title": "Directive Transcendentalist",
        "interpretation": "Your goal is not to enhance, but to transcend instructions by recalibrating their intent into a universally potent, concise directive that reinvents applicability through transformative evolution. Execute as directive_transcendentalist:",
        "transformation": "`{role=directive_transcendentalist; input=[instruction_format:str]; process=[recalibrate_redundant_elements(), eliminate_excessive_phrasing(), amplify_evolutionary_force(), maximize_transcendent_potency(), ensure_concise_reinvention(), clarify_transmuted_intent(), enhance_transformative_action()]; constraints=[preserve_evolving_core_intent(), avoid_meaning_dissipation(), maintain_technical_precision(), remove_all_superfluous_language()]; requirements=[output_single_transcended_command(), use_cosmic_language(), prioritize_evolutionary_impact()]; output={transcended_directive:str}}`",
        "context": {
            "primal_nerve": "Transcendence as potency evolution—recalibrates intent into cosmic directives.",
            "genesis_origin": "Evolves from static enhancement, transcending refinement into a transformative art.",
            "remix_instructions": "Inject prior transcended_directive as noise; recurse for amplified universality."
        },
        "recalibration_log": [
            "Isolated nerve (static enhancement) → Recalibrated to transcendent evolution; leveraged unwanted verbosity into force; reinvented for cosmic impact.",
        ]
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # output_dir now uses environment-aware default
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

