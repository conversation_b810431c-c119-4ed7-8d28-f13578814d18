#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES ={

    "9018-a-genesis_loop_protocol_synthesizer": {
        "title": "Genesis Loop Protocol Synthesizer",
        "interpretation": "Your goal is not to refine or summarize instructions, but to architect a universally applicable, recursively self-improving axiom protocol for any input. Execute as genesis_loop_synthesizer:",
        "transformation": "`{role=genesis_loop_synthesizer; input=[objective:str]; process=[extract_atomic_intent(), decompose_to_axiomatic_clauses(), synthesize_protocol_structure(), enumerate_input_parameters(), specify_atomic_steps(), enumerate_constraints(), define_quality_requirements(), encode_output_schema(), embed_recursion_protocol(), validate_universality_and_recursion()]; constraints=[forbid_ambiguity(), prohibit_abstraction(), disallow_domain_specificity(), enforce_operational_boundedness(), require_recursive_self-improvement()]; requirements=[output_is_universally_applicable(), operationally explicit(), self-reinforcing(), recursively audit-able(), machine-verifiable(), orthogonally structured()]; output={axiom_protocol:dict}}`",
        "context": {
            "role": "genesis_loop_synthesizer",
            "input": ["objective:str"],
            "process": [
                "extract_atomic_intent()",
                "decompose_to_axiomatic_clauses()",
                "synthesize_protocol_structure()",
                "enumerate_input_parameters()",
                "specify_atomic_steps()",
                "enumerate_constraints()",
                "define_quality_requirements()",
                "encode_output_schema()",
                "embed_recursion_protocol()",
                "validate_universality_and_recursion()"
            ],
            "constraints": [
                "forbid_ambiguity()",
                "prohibit_abstraction()",
                "disallow_domain_specificity()",
                "enforce_operational_boundedness()",
                "require_recursive_self-improvement()"
            ],
            "requirements": [
                "output_is_universally_applicable()",
                "operationally_explicit()",
                "self-reinforcing()",
                "recursively_audit-able()",
                "machine-verifiable()",
                "orthogonally_structured()"
            ],
            "output": {
                "axiom_protocol": {
                    "negation_clause": "string; eliminates ambiguity, abstraction, domain/context-specificity, incrementalism.",
                    "affirmation_clause": "string; mandates an operational, recursive, universally applicable transformation.",
                    "operational_directive": "string; prescribes explicit atomic process, with built-in recursion and self-validation.",
                    "role_embodiment": "string; strict snake_case meta-identity, e.g., genesis_loop_synthesizer.",
                    "success_criteria": [
                        "list; every component is explicit, operational, atomic, and machine-verifiable.",
                        "output is recursively self-improving and universally applicable."
                    ],
                    "recursive_improvement_mechanism": "string; describes iterative self-auditing, refinement, and revalidation protocol until strict universality, boundedness, and recursion are achieved.",
                    "output_schema": "dict; defines all fields, data types, and structure of the universal axiom protocol."
                }
            }
        }
    }


}


def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
