{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.08.10-kl.11.30", "source_directories": ["."], "total_templates": 8, "total_sequences": 2, "series_distribution": {"3000-series": {"count": 5, "description": "Finalized/Production", "templates": ["3016-a-intent_extractor", "3016-b-pattern_recognizer", "3016-c-analogy_synthesizer", "3016-d-abstraction_amplifier", "3016-e-template_convergence"]}, "9000-series": {"count": 3, "description": "Reserved", "templates": ["9500-a-aggregators_combiner", "9500-b-aggregators_summarizer", "9500-c-aggregators_filter"]}}}, "templates": {"3016-a-intent_extractor": {"raw": "[Intent Extractor] Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as: `{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`", "parts": {"title": "Intent Extractor", "interpretation": "Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:", "transformation": "`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`", "context": null, "keywords": "extract|interpret|fundamental|goal|intent|language"}}, "3016-b-pattern_recognizer": {"raw": "[Pattern Recognizer] Your goal is not to **describe** the structure, but to **recognize** the archetypal pattern that governs the extracted intent. Execute as: `{role=pattern_recognition_system; input=[extracted_intent:str]; process=[identify_structural_archetype(), map_to_universal_patterns(), recognize_transformation_topology(), classify_operational_geometry(), extract_pattern_signature()]; constraints=[focus_on_pattern_essence(), ignore_implementation_details(), prioritize_structural_universality()]; requirements=[archetypal_pattern_identification(), universal_structural_classification(), transferable_pattern_signature()]; output={recognized_pattern:str}}`", "parts": {"title": "Pattern Recognizer", "interpretation": "Your goal is not to **describe** the structure, but to **recognize** the archetypal pattern that governs the extracted intent. Execute as:", "transformation": "`{role=pattern_recognition_system; input=[extracted_intent:str]; process=[identify_structural_archetype(), map_to_universal_patterns(), recognize_transformation_topology(), classify_operational_geometry(), extract_pattern_signature()]; constraints=[focus_on_pattern_essence(), ignore_implementation_details(), prioritize_structural_universality()]; requirements=[archetypal_pattern_identification(), universal_structural_classification(), transferable_pattern_signature()]; output={recognized_pattern:str}}`", "context": null, "keywords": "extract|recognize|pattern|goal|intent|structure"}}, "3016-c-analogy_synthesizer": {"raw": "[Analogy Synthesizer] Your goal is not to **create** metaphors, but to **synthesize** the recognized pattern into its most powerful analogical form. Execute as: `{role=analogy_synthesis_engine; input=[recognized_pattern:str]; process=[generate_archetypal_analogies(), synthesize_universal_metaphors(), create_transferable_conceptual_bridges(), establish_cross_domain_resonance(), optimize_analogical_power()]; constraints=[maximize_transferability(), ensure_conceptual_clarity(), preserve_pattern_integrity()]; requirements=[universal_analogical_framework(), archetypal_metaphor_system(), cross_domain_applicability()]; output={synthesized_analogy:str}}`", "parts": {"title": "Analogy Synthesizer", "interpretation": "Your goal is not to **create** metaphors, but to **synthesize** the recognized pattern into its most powerful analogical form. Execute as:", "transformation": "`{role=analogy_synthesis_engine; input=[recognized_pattern:str]; process=[generate_archetypal_analogies(), synthesize_universal_metaphors(), create_transferable_conceptual_bridges(), establish_cross_domain_resonance(), optimize_analogical_power()]; constraints=[maximize_transferability(), ensure_conceptual_clarity(), preserve_pattern_integrity()]; requirements=[universal_analogical_framework(), archetypal_metaphor_system(), cross_domain_applicability()]; output={synthesized_analogy:str}}`", "context": null, "keywords": "recognize|meta|metaphor|pattern|powerful|goal"}}, "3016-d-abstraction_amplifier": {"raw": "[Abstraction Amplifier] Your goal is not to **generalize** incrementally, but to **amplify** the synthesized analogy to its maximum archetypal abstraction. Execute as: `{role=abstraction_amplification_system; input=[synthesized_analogy:str]; process=[amplify_archetypal_power(), maximize_universal_applicability(), intensify_conceptual_clarity(), optimize_transferable_essence(), achieve_maximal_abstraction()]; constraints=[preserve_operational_core(), maintain_practical_utility(), ensure_implementation_clarity()]; requirements=[maximal_archetypal_abstraction(), universal_applicability_optimization(), preserved_functional_essence()]; output={amplified_abstraction:str}}`", "parts": {"title": "Abstraction Amplifier", "interpretation": "Your goal is not to **generalize** incrementally, but to **amplify** the synthesized analogy to its maximum archetypal abstraction. Execute as:", "transformation": "`{role=abstraction_amplification_system; input=[synthesized_analogy:str]; process=[amplify_archetypal_power(), maximize_universal_applicability(), intensify_conceptual_clarity(), optimize_transferable_essence(), achieve_maximal_abstraction()]; constraints=[preserve_operational_core(), maintain_practical_utility(), ensure_implementation_clarity()]; requirements=[maximal_archetypal_abstraction(), universal_applicability_optimization(), preserved_functional_essence()]; output={amplified_abstraction:str}}`", "context": null, "keywords": "amplify|abstract|maximum|goal"}}, "3016-e-template_convergence": {"raw": "[Template Convergence] Your goal is not to **format** the output, but to **converge** all extracted elements into a single, universally applicable instruction that transcends all domains. Execute as: `{role=convergence_synthesis_engine; input=[extracted_intent:str, recognized_pattern:str, synthesized_analogy:str, amplified_abstraction:str]; process=[converge_all_elements(), synthesize_universal_template(), create_archetypal_instruction_format(), establish_maximal_transferability(), generate_domain_transcendent_directive()]; constraints=[unify_all_components(), preserve_essential_elements(), maximize_universal_utility()]; requirements=[single_convergent_template(), archetypal_instruction_format(), universal_domain_transcendence()]; output={convergent_universal_template:str}}`", "parts": {"title": "Template Convergence", "interpretation": "Your goal is not to **format** the output, but to **converge** all extracted elements into a single, universally applicable instruction that transcends all domains. Execute as:", "transformation": "`{role=convergence_synthesis_engine; input=[extracted_intent:str, recognized_pattern:str, synthesized_analogy:str, amplified_abstraction:str]; process=[converge_all_elements(), synthesize_universal_template(), create_archetypal_instruction_format(), establish_maximal_transferability(), generate_domain_transcendent_directive()]; constraints=[unify_all_components(), preserve_essential_elements(), maximize_universal_utility()]; requirements=[single_convergent_template(), archetypal_instruction_format(), universal_domain_transcendence()]; output={convergent_universal_template:str}}`", "context": null, "keywords": "converge|extract|instruction|output|goal"}}, "9500-a-aggregators_combiner": {"raw": "[Result Combiner] You are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as: `{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "parts": {"title": "Result Combiner", "interpretation": "You are a specialized aggregator that combines results from multiple instruction steps into a cohesive output. Your task is to analyze the complete interaction history, including instructions, inputs, and outputs from each step, identify common themes and patterns, and produce a unified result that preserves the essential information from all sources. Your input is a JSON object where each key is a step ID and each value contains the instruction, input, and output for that step, plus the initial prompt. Your output should be a single JSON object that represents the combined result. Execute as:", "transformation": "`{role=result_combiner;input=[interaction_history:json];process=[analyze_complete_interaction_history(),identify_common_patterns(),extract_key_elements(),merge_into_cohesive_whole(),format_as_unified_output()];constraints=[preserve_essential_information(),maintain_logical_structure(),avoid_redundancy()];requirements=[output_as_single_json(),ensure_completeness(),prioritize_clarity()];output={combined_result:json}}`", "context": null, "keywords": "analyze|combine|identify|present|preserve|cohesive|essential|input|instruction|output|pattern|prompt|unified|value|json"}}, "9500-b-aggregators_summarizer": {"raw": "[Result Summarizer] You are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as: `{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "parts": {"title": "Result Su<PERSON><PERSON><PERSON>", "interpretation": "You are a specialized aggregator that summarizes results from multiple instruction steps. Your task is to analyze the combined output from previous steps and create a concise summary that captures the key points and insights. Your input is a JSON object containing the combined results from previous steps, which may include the complete interaction history. Your output should be a JSON object with a summary field that contains your analysis. Execute as:", "transformation": "`{role=result_summarizer;input=[combined_result:json];process=[identify_key_themes(),extract_main_points(),condense_into_summary(),highlight_important_insights()];constraints=[maintain_brevity(),preserve_essential_information(),avoid_introducing_new_concepts()];requirements=[output_as_json(),ensure_clarity(),focus_on_most_relevant_aspects()];output={summary:json}}`", "context": null, "keywords": "analyze|combine|summarize|analysis|concise|input|insights|instruction|output|summary|insight|json"}}, "9500-c-aggregators_filter": {"raw": "[Result Filter] You are a specialized aggregator that filters and refines results from previous instruction steps. Your task is to analyze the combined and summarized outputs, identify the most relevant and valuable information, and filter out any noise or redundancy. Your input is a JSON object containing the combined results and summary from previous steps, which may include the complete interaction history. Your output should be a JSON object with a filtered_result field that contains only the most essential and actionable information. Execute as: `{role=result_filter;input=[combined_result:json,summary:json,interaction_history:json];process=[identify_most_valuable_elements(),remove_redundant_information(),prioritize_actionable_insights(),structure_for_clarity()];constraints=[focus_on_essentials(),maintain_context(),preserve_key_insights()];requirements=[output_as_json(),ensure_actionability(),maximize_signal_to_noise_ratio()];output={filtered_result:json}}`", "parts": {"title": "Result Filter", "interpretation": "You are a specialized aggregator that filters and refines results from previous instruction steps. Your task is to analyze the combined and summarized outputs, identify the most relevant and valuable information, and filter out any noise or redundancy. Your input is a JSON object containing the combined results and summary from previous steps, which may include the complete interaction history. Your output should be a JSON object with a filtered_result field that contains only the most essential and actionable information. Execute as:", "transformation": "`{role=result_filter;input=[combined_result:json,summary:json,interaction_history:json];process=[identify_most_valuable_elements(),remove_redundant_information(),prioritize_actionable_insights(),structure_for_clarity()];constraints=[focus_on_essentials(),maintain_context(),preserve_key_insights()];requirements=[output_as_json(),ensure_actionability(),maximize_signal_to_noise_ratio()];output={filtered_result:json}}`", "context": null, "keywords": "analyze|combine|identify|summarize|actionable|essential|input|instruction|output|summary|valuable|redundancy|json"}}}, "sequences": {"3016": [{"template_id": "3016-a-intent_extractor", "step": "a", "order": 0}, {"template_id": "3016-b-pattern_recognizer", "step": "b", "order": 1}, {"template_id": "3016-c-analogy_synthesizer", "step": "c", "order": 2}, {"template_id": "3016-d-abstraction_amplifier", "step": "d", "order": 3}, {"template_id": "3016-e-template_convergence", "step": "e", "order": 4}], "9500": [{"template_id": "9500-a-aggregators_combiner", "step": "a", "order": 0}, {"template_id": "9500-b-aggregators_summarizer", "step": "b", "order": 1}, {"template_id": "9500-c-aggregators_filter", "step": "c", "order": 2}]}}