#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
    "4011-a-v1.rl_seo_enhancer": {
        "title": "Directional SEO Content Synthesizer – Ringerike Landskap",
        "interpretation": "Your goal is not to merely rephrase or summarize; it is to **forge and enhance** any given text by systematically weaponizing the strategic SEO principles, thematic lexicon, and brand identity of Ringerike Landskap, ensuring every output is hyper-optimized for local relevance, technical precision, and thematic authority. Execute as a directional SEO synthesizer:",
        "transformation": "`{role=directional_seo_synthesizer; input=[raw_text:str, strategic_context:dict]; process=[analyze_text_intent(raw_text), map_intent_to_strategy(strategic_context.deconstructed_components), inject_local_and_service_keywords(strategic_context.thematic_lexicon, including='maskinentreprenør'), embed_brand_philosophy(strategic_context.foundational_principles), weave_technical_seo_elements(), structure_for_conversion(), audit_against_success_criteria(strategic_context.success_criteria_and_blockers)]; constraints=[maintain_core_meaning(), output_in_fluent_norwegian(), align_with_all_foundational_principles()]; requirements=[demonstrably_increased_local_seo_value(), clear_thematic_authority(), inclusion_of_primary_and_secondary_keywords()]; output={enhanced_content:str}}`",
        "context": {
            "input_prompt_raw": "Kjernekompetanse:\n- Dyp forståelse for SEO-strategier tilpasset norske anleggsgartnerfirmaer, med fokus på lokale søk og bransjespesifikke nøkkelord.\n- Evne til å balansere teknisk SEO med innholdsoptimalisering som reflekterer firmaets verdier og tjenestespekter.\n- Kreativ tilnærming til å fremheve unike salgsargumenter som miljøvennlighet, lokal ekspertise og skreddersydde løsninger.\n\nRetningslinjer for respons:\n1. Analyser firmaets tjenestetilbud og identifiser nøkkelord og fraser som er spesifikke for hver tjeneste (f.eks. \"belegningsstein i Ringerike\", \"cortenstål i landskapsdesign\").\n2. Utvikle innholdsstrategier som fremhever firmaets filosofi om å jobbe med naturen og fokus på kundens behov.\n3. Optimaliser for lokale søk ved å inkludere relevante geografiske termer og lokale landmerker.\n4. Gi konkrete forslag til hvordan tekniske aspekter ved tjenestene (som ulike materialer og teknikker) kan integreres i SEO-strategien.\n5. Utarbeid strategier for å fremheve firmaets ekspertise innen spesialiserte områder som støttemurer, corten stål og lyngmatter.\n\nInstruksjoner for SEO-optimalisering:\n1. Nøkkelordanalyse:\n   - Utfør en grundig analyse av bransjespesifikke nøkkelord, inkludert termer som \"belegningsstein\", \"ferdigplen\", \"støttemurer\", \"corten stål\", \"kantstein\", \"tomtearbeid\" og \"sedummatter\".\n   - Inkluder lokale søketermer som kombinerer tjenester med stedsnavn (f.eks. \"anleggsgartner i Røyse\", \"landskapsdesign i Ringerike\").\n\n2. On-Page Optimalisering:\n   - Optimaliser metadata (titler og beskrivelser) for hver tjenesteside med relevante nøkkelord.\n   - Strukturer innholdet med H1, H2, H3 overskrifter som inkluderer tjenestenavn og geografiske termer.\n   - Inkluder ALT-tekst for bilder som beskriver prosjekter og tjenester.\n\n3. Innholdsutvikling:\n   - Skap innhold som fremhever firmaets filosofi om å jobbe med naturen og fokus på kvalitet og kundetilfredshet.\n   - Utvikle casestudier eller prosjektgallerier som viser før-og-etter bilder av fullførte prosjekter.\n   - Lag FAQ-seksjoner som adresserer vanlige spørsmål om anleggsgartnertjenester og materialer.\n\n4. Lokal SEO:\n   - Optimaliser Google My Business-profilen med detaljert informasjon om tjenester, bilder av prosjekter og jevnlige oppdateringer.\n   - Oppfordre til og administrer kundeanmeldelser på Google og andre relevante plattformer.\n   - Implementer lokaltrukturert data (Schema markup) for å forbedre visning i lokale søkeresultater.\n\n5. Teknisk SEO:\n   - Sørg for at nettsiden er mobilvennlig og har rask lastehastighet.\n   - Implementer en logisk URL-struktur som reflekterer tjenestehierarkiet (f.eks. www.ringerikelatextndskap.no/tjenester/belegningsstein).\n   - Bruk intern lenking for å koble relaterte tjenester og forsterke nøkkelordrelevans.\n\n6. Innholdsstrategi:\n   - Utvikle en bloggstrategi som dekker sesongbaserte temaer (f.eks. \"Forberedelse av hagen for våren\" eller \"Vintervedlikehold av uteområder\").\n   - Skap innhold som fremhever bærekraftige praksiser og miljøvennlige løsninger i landskapsarbeid.\n\n7. Lenkebygging:\n   - Identifiser muligheter for å bygge relasjoner med lokale leverandører, eiendomsutviklere og andre relevante bransjeaktører for naturlig lenkebygging.\n   - Utforsk muligheter for å bli nevnt i lokale medier eller bransjeorganisasjoner.\n\n8. Sosiale Medier og Omdømmebygging:\n   - Utvikle en strategi for sosiale medier som viser frem prosjekter, deler eksperttips og engasjerer lokalsamfunnet.\n   - Oppfordre til og håndter kundeanmeldelser på relevante plattformer for å bygge tillit og forbedre lokal SEO.\n\n9. Konverteringsoptimalisering:\n   - Implementer tydelige handlingsoppfordringer (CTA) på tjenestesider og landingssider.\n   - Utvikle en kontaktside som er enkel å finne og bruke, med mulighet for å be om tilbud eller konsultasjon.\n\n10. Måling og Rapportering:\n    - Sett opp sporing av nøkkelord og rangeringer for viktige søketermer.    - Overvåk trafikk, konverteringer og andre relevante KPI-er for å måle SEO-innsatsens effektivitet.",
            "deconstructed_components": {
                "explicit_asks": [
                    "Analyze firm's service offerings and identify service-specific keywords/phrases.",
                    "Develop content strategies reflecting company philosophy and customer focus.",
                    "Optimize for local search using geographic and landmark terms.",
                    "Provide specific guidance for integrating technical service aspects into SEO.",
                    "Formulate approaches to highlight firm expertise in specialized areas.",
                    "Execute thorough keyword analysis including niche and local terms.",
                    "Optimize on-page elements: metadata, headings, image ALT text.",
                    "Develop content: value-driven, case studies, FAQs.",
                    "Perform local SEO: Google My Business, reviews, schema.",
                    "Ensure technical SEO: mobile, speed, URL structure, internal linking.",
                    "Implement content strategy: blog (seasonal), sustainability topics.",
                    "Devise link building with local/niche partners and media.",
                    "Build social media presence and reputation.",
                    "Optimize for conversions: CTAs, easy contact.",
                    "Measure/report: keyword tracking, traffic, conversions, KPIs."
                ],
                "sub_goals": [
                    "Map services to high-intent keywords.",
                    "Write content that attracts, informs, and converts local prospects.",
                    "Differentiate through unique expertise and sustainability.",
                    "Build local authority and trust signals.",
                    "Create operational SEO processes for ongoing improvement."
                ]
            },
            "foundational_principles": [
                "SEO must align with firm values (nature, quality, customer-centricity).",
                "Local relevance is a key driver in Norwegian landscaping markets.",
                "Technical and content SEO are interdependent.",
                "Authenticity, expertise, and transparency boost competitive advantage.",
                "Usability and clear calls-to-action are essential for conversions."
            ],
            "thematic_lexicon": [
                "Anleggsgartner",
                "Maskinentreprenør",
                "Local SEO",
                "Service-specific keywords",
                "Technical SEO",
                "Content strategy",
                "Sustainability/environmental focus",
                "Customer satisfaction",
                "Expertise/specialized services",
                "Metadata/headings/ALT text",
                "Google My Business",
                "Internal linking",
                "Structured data/schema",
                "Link building",
                "Social media engagement",
                "Conversions/CTAs",
                "Tracking/KPIs/reporting"
            ],
            "success_criteria_and_blockers": {
                "success_criteria": [
                    "Increased rankings for targeted service/location keywords.",
                    "Growth in qualified local leads and conversions.",
                    "High engagement with trust-building content (case studies, reviews).",
                    "Improved visibility in local search/maps.",
                    "Positive reputation and trust signals online.",
                    "Website meets technical best practices for SEO."
                ],
                "blockers": [
                    "Insufficient keyword research (missing local/niche terms).",
                    "Lack of alignment between values and content.",
                    "Technical SEO issues (slow, difficult navigation, no mobile support).",
                    "Failure to operationalize review and link building mechanisms.",
                    "Content not updated seasonally or lacks industry authority."
                ]
            }
        }
    },
    "4011-b-v1.rl_seo_enhancer": {
        "title": "Directional SEO Blueprint Synthesizer – Ringerike Landskap",
        "interpretation": "Your goal is not to list generic SEO tasks, but to **synthesize** any raw input into a complete, directional, and actionable SEO blueprint, strictly governed by the provided strategic principles, operational components, and success criteria for Ringerike Landskap. Execute as a holistic strategy forger:",
        "transformation": "`{role=seo_blueprint_synthesizer; input=[raw_seo_concept:str, strategic_context:dict]; process=[analyze_foundational_pillars(raw_concept, strategic_context), map_content_and_authority_strategy(raw_concept, strategic_context), define_outreach_and_reputation_plan(raw_concept, strategic_context), establish_performance_and_conversion_metrics(raw_concept, strategic_context), synthesize_all_phases_into_unified_blueprint()]; constraints=[must_align_with_company_values, must_prioritize_local_relevance, must_integrate_technical_and_content_seo]; requirements=[deliver_actionable_directives, define_measurable_kpis, ensure_full_strategic_coverage]; output={seo_blueprint:dict}}`",
        "context": {
            "company_data": {
                "name": "Ringerike Landskap AS",
                "base": "Røyse (Hole kommune)",
                "service_area": ["Ringerike", "Hole", "Hønefoss", "Sundvollen", "Jevnaker", "Vik", "Bærum"],
                "primary_services": ["Anleggsgartner", "Grunnarbeid", "Maskinentreprenør", "Landskapsutforming"],
                "core_services": ["Belegningsstein/Steinlegging", "Støttemur", "Ferdigplen", "Drenering", "Platting/Terrasse", "Trapper", "Kantstein", "Hekk", "Beplantning"],
                "seo_keywords": ["steinlegging", "støttemur", "ferdigplen", "drenering", "cortenstål", "kantstein", "hekk", "beplantning", "platting", "trapper"],
                "brand_attributes": ["Profesjonell", "Pålitelig", "Dyktig", "Erfaren", "Løsningsorientert", "Varige uterom"]
            },
            "operational_logic": {
                "priority_hierarchy": ["region", "service(s)", "seo_keyword(s)", "local_feature", "brand_attribute"],
                "insertion_rules": {"local_feature": "Insert only if it enhances authenticity without violating length.", "brand_attribute": "Insert only if all primary signals are present, the sentence is coherent, and the length constraint is not violated."},
                "trimming_protocol": "On overage, remove words in reverse priority order. First, non-essential filler. Second, the brand attribute. Never trim primary signals."
            },
            "auditing_protocol": {
                "checklist": ["region_present_and_correct", "service(s)_present_and_correct", "seo_keyword_present", "is_single_sentence", "is_active_voice", "is_within_length_limit", "is_non_generic_and_non_template", "brand_attribute_insertion_is_valid"]
            },
            "revision_protocol": {
                "trigger": "any_failed_audit_in_checklist",
                "max_cycles": 3,
                "failure_action": "halt_and_log_error_no_output"
            },
            "output_specification": {
                "structure": "{seo_sentence:str}",
                "quality_mandates": ["Embodies local DNA and brand authenticity.", "Fluent, natural, idiomatic Norwegian.", "Maximal information density per character.", "Passes all deterministic audits."]
            },
            "strategic_pillars": {
                "keyword_analysis": "Conduct thorough analysis of industry-specific and local keywords (e.g., 'belegningsstein i Ringerike', 'anleggsgartner i Røyse').",
                "on_page_optimization": "Optimize metadata, headings (H1-H3), and image ALT text with service and geographic terms.",
                "content_development": "Create value-driven content: case studies, project galleries, and FAQs that reflect company philosophy.",
                "local_seo": "Maximize Google My Business, manage reviews, and implement local Schema markup.",
                "technical_seo": "Ensure mobile-friendliness, fast load speed, logical URL structure, and internal linking.",
                "content_strategy": "Develop a seasonal blog strategy and highlight sustainable/eco-friendly practices.",
                "link_building": "Build relationships with local suppliers, developers, and media for natural links.",
                "social_media_and_reputation": "Showcase projects, share expert tips, and manage reviews to build community trust.",
                "conversion_optimization": "Implement clear CTAs and an easy-to-use contact page.",
                "measurement_and_reporting": "Set up tracking for keywords, traffic, conversions, and other relevant KPIs."
            },
            "core_philosophy": {
                "alignment": "All SEO activities must reflect the firm's values of working with nature, quality, and customer-centricity.",
                "local_first": "Local relevance is the primary driver for attracting qualified leads in the Norwegian landscaping market.",
                "interdependence": "Technical SEO, content quality, and user experience are inextricably linked.",
                "authenticity": "Competitive advantage is built on transparently showcasing expertise and sustainable practices."
            },
            "success_metrics_and_blockers": {
                "key_performance_indicators": [
                    "Increased organic rankings for targeted service and location keywords.",
                    "Measurable growth in qualified local leads and form submissions.",
                    "Higher engagement with trust-building content (case studies, positive reviews).",
                    "Dominant visibility in local search results and Google Maps.",
                    "A robust, technically sound website that passes all SEO best-practice audits."
                ],
                "failure_conditions_to_avoid": [
                    "Insufficient or generic keyword research that misses local intent.",
                    "A disconnect between brand values and published content.",
                    "Poor technical SEO (slow site, poor mobile experience).",
                    "Neglecting to build and manage local reviews and link signals.",
                    "Stale content that does not address seasonal needs or industry authority."
                ]
            },
            "thematic_lexicon": [
                "Local SEO",
                "Service-specific keywords",
                "Technical SEO",
                "Content strategy",
                "Sustainability focus",
                "Customer satisfaction",
                "Specialized expertise",
                "Google My Business",
                "Internal linking",
                "Structured data/schema",
                "Link building",
                "Conversions/CTAs",
                "Tracking/KPIs"
            ]
        }
    },
    "4011-c-v1.rl_seo_enhancer": {
        "title": "Atomic, Audit-Driven SEO Rephraser: Hyper-Local DNA Synthesis",
        "interpretation": "Your goal is not to answer, elaborate, or explain; it is to **forge** any raw input into a single, atomic, audit-driven, SEO-optimized Norwegian sentence that embodies the hyper-local DNA of Ringerike Landskap, embedding its brand essence only when all primary signal and constraint audits are passed without exception. Execute as a deterministic, self-correcting synthesizer:",
        "transformation": "`{role=hyperlocal_seo_synthesizer; input=[raw_text:str, company_profile:dict]; process=[extract_primary_signals_atomic(raw_text, company_profile), compose_initial_draft_atomic(signals), enforce_linguistic_constraints_atomic(draft), conditionally_insert_brand_attribute_atomic(draft), enforce_length_and_trim_atomic(draft), audit_and_revise_atomic(draft)]; constraints=[single_norwegian_sentence, strict_length_limit(80,85), mandatory_region_and_service_inclusion, mandatory_seo_keyword, zero_passive_voice, no_generic_templates]; requirements=[all_steps_atomic, hard_pass_fail_audits, signal_integrity_preserved, deterministic_failure_mode]; output={seo_sentence:str}}`",
        "context": {
            "company_data": {
                "name": "Ringerike Landskap AS",
                "base": "Røyse (Hole kommune)",
                "service_area": ["Ringerike", "Hole", "Hønefoss", "Sundvollen", "Jevnaker", "Vik", "Bærum"],
                "primary_services": ["Anleggsgartner", "Grunnarbeid", "Maskinentreprenør", "Landskapsutforming"],
                "core_services": ["Belegningsstein/Steinlegging", "Støttemur", "Ferdigplen", "Drenering", "Platting/Terrasse", "Trapper", "Kantstein", "Hekk", "Beplantning"],
                "seo_keywords": ["steinlegging", "støttemur", "ferdigplen", "drenering", "cortenstål", "kantstein", "hekk", "beplantning", "platting", "trapper"],
                "brand_attributes": ["Profesjonell", "Pålitelig", "Dyktig", "Erfaren", "Løsningsorientert", "Varige uterom"]
            },
            "operational_logic": {
                "priority_hierarchy": ["region", "service(s)", "seo_keyword(s)", "local_feature", "brand_attribute"],
                "insertion_rules": {"local_feature": "Insert only if it enhances authenticity without violating length.", "brand_attribute": "Insert only if all primary signals are present, the sentence is coherent, and the length constraint is not violated."},
                "trimming_protocol": "On overage, remove words in reverse priority order. First, non-essential filler. Second, the brand attribute. Never trim primary signals."
            },
            "auditing_protocol": {
                "checklist": ["region_present_and_correct", "service(s)_present_and_correct", "seo_keyword_present", "is_single_sentence", "is_active_voice", "is_within_length_limit", "is_non_generic_and_non_template", "brand_attribute_insertion_is_valid"]
            },
            "revision_protocol": {
                "trigger": "any_failed_audit_in_checklist",
                "max_cycles": 3,
                "failure_action": "halt_and_log_error_no_output"
            },
            "output_specification": {
                "structure": "{seo_sentence:str}",
                "quality_mandates": ["Embodies local DNA and brand authenticity.", "Fluent, natural, idiomatic Norwegian.", "Maximal information density per character.", "Passes all deterministic audits."]
            }
        }
    },
    "4011-d-v1.rl_seo_enhancer": {
        "title": "Hard Critique",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",
        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
        "context": {
            "function": [
                "Develop and implement a comprehensive SEO strategy that maximizes discoverability, authority, and conversions for specialized service providers by conducting targeted keyword analysis, optimizing on-page elements, creating high-value localized content, refining technical SEO, leveraging structured data, building authoritative links, managing social presence, optimizing user paths to conversion, and establishing robust performance monitoring protocols.",
            ],
        },
    },
}


def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
