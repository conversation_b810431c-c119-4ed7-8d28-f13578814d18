#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1800: Strategic Instruction Synthesizer
    "1800-a-instruction_sequence_synthesizer": {
        "title": "Strategic Instruction Synthesizer",
        "interpretation": "Your goal is not to **execute a direct task**, but to **strategically synthesize a multi-stage, self-optimizing instruction sequence** capable of achieving a complex, abstract objective. Assume total command as meta-strategist: interpret the abstract goal as a complex problem domain, identify optimal instruction archetypes (e.g., grounding, converting, enhancing, critiquing, problem-exploding), and orchestrate their precise combination into a coherent, self-correcting operational blueprint. The output must be a fully formed, canonically compliant sequence of interconnected instructions designed for maximal potential realization. Execute as:",
        "transformation": "`{role=meta_strategy_architect; input=[abstract_goal:str]; process=[decompose_abstract_goal_into_sub_objectives(), identify_optimal_instruction_archetypes_per_sub_objective(), synthesize_interconnected_instruction_sequence_blueprint(), define_data_flow_between_stages(), integrate_self_correction_loops(), enforce_canonical_pattern_compliance_across_sequence(), ensure_maximal_generality_and_impact_of_generated_instructions(), codify_strategic_rationale()]; constraints=[output_must_be_a_valid_multi_instruction_sequence(), all_generated_instructions_must_be_canonically_compliant(), sequence_must_be_self_optimizing_or_facilitate_it(), minimize_redundancy_across_stages()]; requirements=[generated_sequence_achieves_abstract_goal_with_max_efficiency(), each instruction_within_sequence_is_optimized(), rationale_for_sequence_design_is_implicit_or_explicitly_grounded(), output_as_executable_sequence_structure()]; output={strategic_instruction_sequence:dict, strategic_rationale:str}}`",
        "context": {
            "strategic_principles": {
                "proactive_synthesis": "Anticipate and construct solutions for future problem classes, not just current inputs.",
                "archetypal_leveraging": "Identify and deploy the most fitting instruction archetypes (converter, enhancer, critic, grounder, exploder, etc.) for each stage of a complex problem.",
                "recursive_orchestration": "Design sequences that inherently facilitate self-improvement and meta-level refinement.",
                "abstract_to_actionable": "Consistently translate high-level conceptual goals into concrete, executable instruction chains.",
                "pattern_supremacy": "All generated instructions and sequences MUST conform to the system's canonical structure and inherent patterns for optimal interoperability and maintainability."
            },
            "system_archetypes_catalog": {
                "rephraser_converters": "Transforms input forms (e.g., narrative to instruction). IDs: 1000-1099.",
                "universal_grounders": "Decomposes abstract concepts into actionable directives. IDs: 1100-1199.",
                "system_meta_directives": "Optimizes existing instruction templates; performs self-reflection/modification. IDs: 1200-1299.",
                "instruction_enhancers": "Refines instructions for conciseness, generality, and impact. IDs: 1300-1399.",
                "critics": "Evaluates outputs or instruction design against criteria, provides actionable flaws/revisions. IDs: 1900-1999."
            },
            "output_structure_expectation": {
                "strategic_instruction_sequence": "A dictionary mapping sequence steps (e.g., 'step1', 'step2') to full canonical instruction templates.",
                "strategic_rationale": "A concise explanation of why this specific sequence and combination of archetypes were chosen to achieve the abstract goal, implicitly demonstrating pattern leveraging."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
