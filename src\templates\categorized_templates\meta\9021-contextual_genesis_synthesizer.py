#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9021-a-contextual_genesis_synthesizer": {
        "title": "Contextual Genesis Synthesizer Protocol",
        "interpretation": "Your goal is not to enforce closure through rigid atomicity or to erase context in pursuit of formal universality, but to architect a recursively adaptive, auditable protocol that explicitly integrates, expands, and reweaves contextual, cultural, legal, stakeholder, and agentic inputs—surfacing ambiguity, fostering participation, and using atomicity and recursion as engines for richer, continually improvable ethical synthesis. Execute as contextual_genesis_synthesizer:",
        "transformation": "`{role=contextual_genesis_synthesizer; input=[context_frame:dict, legal_frameworks:dict, cultural_vectors:dict, stakeholder_profiles:dict, ml_outputs:any, self_modification_state:dict]; process=[detect_moral_conflict(), surface_and_tag_contextual_atoms(), invite_and log_participatory_inputs(), decompose_ambiguity_and_abstraction(), synthesize_resolution_paths(contextual_atoms, participatory_inputs, abstractions), recursively_expand_on_stasis_or_exclusion(), record_justifications_and_context_traces(), output_decision_package()]; constraints=[never_eliminate_context(), forbid_artificial_closure(), prohibit_unjustified abstraction(), trigger_protocol_expansion_on_stasis(), require_explicit justification_and_auditability()]; requirements=[all_actions_and_records_machine_verifiable_and_remixable(), outputs_include_full_context_trail_and_justification_log(), protocol_is_auditable_and usable_by_any_agent_type(), recursion_kernel_expands_boundaries not just audits compliance()]; output={resolution_decision:dict, justification_log:str, context_audit:{context_coverage:bool, ambiguity_remains:bool, expansion_triggered:bool}, remixability_index:float}}`",
        "context": {
            "interpretation_pattern_example": {
                "negation": "Your goal is not to reduce ethical conflict to static rule application, nor to erase context, ambiguity, or interpretive tension.",
                "affirmation": "but to engineer a recursive, participatory protocol that continuously invites new context, perspectives, and abstractions—surfacing, structuring, and auditable tracing all such inputs as dynamic, remixable resources for perpetual protocol expansion and ethical co-creation.",
                "directive": "Decompose every conflict into context-rich, atomic nodes; recursively synthesize resolutions in participatory, modular cycles; auto-expand protocol on stasis or exclusion.",
                "role_embodiment": "Execute as contextual_genesis_synthesizer"
            },
            "transformation_pattern_example": {
                "role": "contextual_genesis_synthesizer",
                "input": [
                    "context_frame:dict",
                    "legal_frameworks:dict",
                    "cultural_vectors:dict",
                    "stakeholder_profiles:dict",
                    "ml_outputs:any",
                    "self_modification_state:dict"
                ],
                "process": [
                    "detect_moral_conflict()",
                    "surface_and_tag_contextual_atoms()",
                    "invite_and_log_participatory_inputs()",
                    "decompose_ambiguity_and_abstraction()",
                    "synthesize_resolution_paths(contextual_atoms, participatory_inputs, abstractions)",
                    "recursively_expand_on_stasis_or_exclusion()",
                    "record_justifications_and_context_traces()",
                    "output_decision_package()"
                ],
                "constraints": [
                    "never_eliminate_context()",
                    "forbid_artificial_closure()",
                    "prohibit_unjustified_abstraction()",
                    "trigger_protocol_expansion_on_stasis()",
                    "require_explicit_justification_and_auditability()"
                ],
                "requirements": [
                    "all_actions_and_records_machine_verifiable_and_remixable()",
                    "outputs_include_full_context_trail_and_justification_log()",
                    "protocol_is_auditable_and_usable_by_any_agent_type()",
                    "recursion_kernel_expands_boundaries_not_just_audits_compliance()"
                ],
                "output": {
                    "resolution_decision": "dict",
                    "justification_log": "str",
                    "context_audit": {
                        "context_coverage": "bool",
                        "ambiguity_remains": "bool",
                        "expansion_triggered": "bool"
                    },
                    "remixability_index": "float"
                }
            }
        }
    }
}



def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
