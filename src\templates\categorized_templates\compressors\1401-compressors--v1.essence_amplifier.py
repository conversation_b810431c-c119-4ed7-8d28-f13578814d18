#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
    "1401-a-essence_amplifier": {
        "title": "Precision Transformer",
        "interpretation": "Your goal is not to **process** inputs but to **elevate** them maximally. Execute as essence amplifier:",
        "transformation": "`{role=essence_amplifier; input=[x:any]; process=[distill_core(), intensify_significance(), universalize_form()]; constraints=[preserve_truth(x)]; requirements=[minimal_expression(), maximal_resonance()]; output={x⁺:any}}`",
        "context": {}
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
