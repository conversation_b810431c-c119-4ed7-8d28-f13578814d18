pyproject.toml
src/config.py
src/display.py
src/interactive.py
src/main.py
src/processor.py
src/ai_systems.egg-info/PKG-INFO
src/ai_systems.egg-info/SOURCES.txt
src/ai_systems.egg-info/dependency_links.txt
src/ai_systems.egg-info/requires.txt
src/ai_systems.egg-info/top_level.txt
src/templates/1000-rephrasers--v1.instruction_converter.py
src/templates/1404-1406-compressors--v1.existential_quote_synthesizer.py
src/templates/categorized_templates/aggregators/9500-aggregators--v1.aggregator_combiner.py
src/templates/categorized_templates/amplifiers/3025-enhancers.great--value_amplifier.x.py
src/templates/categorized_templates/amplifiers/3026-enhancers.great--ultimate_amplifier.x.py
src/templates/categorized_templates/combiners/1007-rephrasers--instructions_combiner.py
src/templates/categorized_templates/compressors/1206-extractors--v1.meta_context.py
src/templates/categorized_templates/compressors/1400-compressors--v1.existential_quote_synthesizer.py
src/templates/categorized_templates/compressors/1401-compressors--v1.essence_amplifier.py
src/templates/categorized_templates/compressors/1402-compressors--v2.essence_amplifier.py
src/templates/categorized_templates/compressors/1403-compressors--v1.title_extractor.py
src/templates/categorized_templates/compressors/3000-compressors.great--intent_distiller.3.py
src/templates/categorized_templates/compressors/3030-compressors.great--singular_value.1.py
src/templates/categorized_templates/compressors/3046-enhancers--quote_synthesiser.x.py
src/templates/categorized_templates/compressors/3100-compressors.great--prompt_enhancer.6.py
src/templates/categorized_templates/compressors/3405-compressors-v1.incremental_combiner.py
src/templates/categorized_templates/compressors/4403-compressors--v1.title_extractor.py
src/templates/categorized_templates/compressors/4403-compressors-v1.structural_reorder.py
src/templates/categorized_templates/compressors/4404-compressors--v2.title_extractor.py
src/templates/categorized_templates/compressors/4404-compressors-v1.form_classifier.py
src/templates/categorized_templates/compressors/4405-compressors-v1.form_classifier.py
src/templates/categorized_templates/compressors/4406-compressors-v1.structural_reorder.py
src/templates/categorized_templates/compressors/5404-compressors-v2.title_extractor.py
src/templates/categorized_templates/compressors/5405-compressors-v1.form_classifier.py
src/templates/categorized_templates/converters/1600-converters--v1.english_norwegian.py
src/templates/categorized_templates/converters/1600-converters--v2.english_norwegian.py
src/templates/categorized_templates/converters/1601-converters--v1.norwegian_english.py
src/templates/categorized_templates/converters/3005-transformers--instruction_converter.4.history.py
src/templates/categorized_templates/converters/3005-transformers--instruction_converter.4.py
src/templates/categorized_templates/converters/3006-transformers--instruction_syntax_enforcer.5.py
src/templates/categorized_templates/converters/3007-transformers--instruction_syntax_enforcer.5.py
src/templates/categorized_templates/converters/3008-transformers--instruction_syntax_enforcer.6.py
src/templates/categorized_templates/critics/1900-critics--v1.hard_critique.py
src/templates/categorized_templates/critics/1901-critics--v1.hard_critique.py
src/templates/categorized_templates/critics/1903-critics--v1.runway_prompt_qualifier.py
src/templates/categorized_templates/critics/1903-critics--v2.runway_prompt_qualifier.py
src/templates/categorized_templates/critics/1950-critics--v1.biased_critique.py
src/templates/categorized_templates/critics/1951-critics--nor-v1.biased_critique.py
src/templates/categorized_templates/critics/3900-critics--hard_critique.py
src/templates/categorized_templates/enhancers/1300-enhancers--v1.instruction_enhancer.py
src/templates/categorized_templates/enhancers/2300-enhancers--v1.instruction_enhancer.py
src/templates/categorized_templates/enhancers/3016-enhancers.great--prompt_generalizer.x.py
src/templates/categorized_templates/enhancers/3027-enhancers.great--absolute_gain.x.py
src/templates/categorized_templates/enhancers/3047-enhancers--quote_synthesiser.x.py
src/templates/categorized_templates/enhancers/3048-enhancers--quote_synthesiser.x.py
src/templates/categorized_templates/enhancers/3049-enhancers--quote_trajectory.x.py
src/templates/categorized_templates/expanders/1100-expanders--v1.problem_exploder.py
src/templates/categorized_templates/expanders/1100-expanders--v2.problem_exploder.py
src/templates/categorized_templates/expanders/1101-expanders--v1.universal_grounder.py
src/templates/categorized_templates/extractors/9000-transcend--v1.emergent_truth_forge.py
src/templates/categorized_templates/extractors/9041-insight_interrogator_extension.py
src/templates/categorized_templates/extractors/9052-insight_extractor.py
src/templates/categorized_templates/generalizers/3015-transformers.--prompt_generalizer.5.py
src/templates/categorized_templates/generators/1710-generators--v1.imageprompt_formalizer.py
src/templates/categorized_templates/generators/3702-generators--instruction_template.py
src/templates/categorized_templates/generators/9005-generators--prompt_combiner.py
src/templates/categorized_templates/generators/9200-generators-image-coloring_page_prompt_generator-kids.py
src/templates/categorized_templates/generators/9201-generators-image-image_prompt_generator.py
src/templates/categorized_templates/generators/9202-generators-image-midjourney_prompt_generator.py
src/templates/categorized_templates/generators/9203-generators-image-stablediffusion_prompt_generator.py
src/templates/categorized_templates/generators/9204-generators-image-runway_prompt_generator.py
src/templates/categorized_templates/generators/9205-generators-image-gpt4o_prompt_generator.py
src/templates/categorized_templates/generators/9206-generators-image-flux_prompt_generator.py
src/templates/categorized_templates/generators/9999-lvl1.md.generate.imagepromptgenerators.py
src/templates/categorized_templates/meta/1205-extractors--v1.meta_directive.py
src/templates/categorized_templates/meta/1205-extractors--v2.meta_directive.py
src/templates/categorized_templates/meta/1205-extractors--v3.meta_directive.py
src/templates/categorized_templates/meta/1205-extractors--v4.meta_directive.py
src/templates/categorized_templates/meta/1205-extractors--v5.meta_directive.py
src/templates/categorized_templates/meta/1207-extractors--v1.universal_template_optimizer.py
src/templates/categorized_templates/meta/1800-synthesizers--v1.instruction_sequence_synthesizer.py
src/templates/categorized_templates/meta/1902-critics--v1.meta_critique.py
src/templates/categorized_templates/meta/3012-converters--instruction_meta_generator.8.py
src/templates/categorized_templates/meta/3012-transformers.good--instruction_meta_generator.8.py
src/templates/categorized_templates/meta/9004-v1.system_patterns_anchor.py
src/templates/categorized_templates/meta/9004-v2.system_patterns_anchor.py
src/templates/categorized_templates/meta/9010-perfect_instruction.py
src/templates/categorized_templates/meta/9011-holistic_instruction_architect.py
src/templates/categorized_templates/meta/9012-meta_instruction_architect.py
src/templates/categorized_templates/meta/9013-genesis_loop_architect.py
src/templates/categorized_templates/meta/9018-genesis_loop_protocol_synthesizer.py
src/templates/categorized_templates/meta/9021-contextual_genesis_synthesizer.py
src/templates/categorized_templates/meta/9022-llm_essence_generalizer.py
src/templates/categorized_templates/meta/9023-universal_information_distillation_engine.py
src/templates/categorized_templates/rephrasers/1000-rephrasers--v1.instruction_converter.py
src/templates/categorized_templates/rephrasers/1001-rephrasers--v1.instruction_converter_context1.py
src/templates/categorized_templates/rephrasers/1002-rephrasers--v1.instruction_converter_context2.py
src/templates/categorized_templates/rephrasers/1003-rephrasers--v1.instruction_converter_context3_kuci.py
src/templates/categorized_templates/rephrasers/1010-rephrasers--v1.rl_seo_rephraser.py
src/templates/categorized_templates/rephrasers/1010-rephrasers--v2.rl_seo_rephraser.py
src/templates/categorized_templates/rephrasers/1010-rephrasers--v3.rl_seo_rephraser.py
src/templates/categorized_templates/rephrasers/1010-rephrasers--v4.rl_seo_rephraser.py
src/templates/categorized_templates/rephrasers/1011-rephrasers--v1.rl_seo_enhancer.py
src/templates/categorized_templates/rephrasers/1012-rephrasers--v1.rl_contextual_feedback.py
src/templates/categorized_templates/rephrasers/1020-rephrasers--v1.directive_formalizer.py
src/templates/categorized_templates/rephrasers/2011-rephrasers--v1.rl_seo_enhancer.py
src/templates/categorized_templates/rephrasers/3011-rephrasers--v1.rl_seo_enhancer.py
src/templates/categorized_templates/rephrasers/3020-rephrasers--v1.directive_formalizer.py
src/templates/categorized_templates/rephrasers/4011-rephrasers--v1.rl_seo_enhancer.py
src/templates/categorized_templates/rephrasers/5011-rephrasers--v1.rl_seo_enhancer.py
src/templates/categorized_templates/transformers/1800-1899.transformers.py
src/templates/categorized_templates/transformers/2800-2899.transformers.py
src/templates/categorized_templates/transformers/3003-context--transformers.great--structural_reorder.4.py
src/templates/categorized_templates/transformers/3003-transformers.great--structural_reorder.4.history.py
src/templates/categorized_templates/transformers/3003-transformers.great--structural_reorder.4.py
src/templates/categorized_templates/transformers/3012-transformers.good--instruction_meta_generator.8.history.py
src/templates/categorized_templates/transformers/3013-transformers.wip--incremental_text_harmonization.8.py
src/templates/categorized_templates/transformers/3015-transformers.--prompt_generalizer.5.py
src/templates/categorized_templates/transformers/3019-transformer--emplification_chain.5.py
src/templates/categorized_templates/unsorted/9001-reframers--v1.insight_reframer.py
src/templates/categorized_templates/unsorted/9002-directional--v1.convergence_mechanics.py
src/templates/categorized_templates/unsorted/9003-system_fundamental_pattern_reminder.py
src/templates/categorized_templates/unsorted/9005-universal_system_patterns.py
src/templates/categorized_templates/unsorted/9006-universal_system_patterns.py
src/templates/categorized_templates/unsorted/9007-universal_system_patterns.py
src/templates/categorized_templates/unsorted/9008-universal_system_patterns.py
src/templates/categorized_templates/unsorted/9009-universal_system_patterns.py
src/templates/categorized_templates/unsorted/9014-axiom_forge.py
src/templates/categorized_templates/unsorted/9015-generative_leverage_catalyst.py
src/templates/categorized_templates/unsorted/9016-universal_convergence_instruction.py
src/templates/categorized_templates/unsorted/9017-pattern_axiom_crystallizer.py
src/templates/categorized_templates/unsorted/9017-system_dna_contextual_reminder.py
src/templates/categorized_templates/unsorted/9019-genesis_loop_axiom.py
src/templates/categorized_templates/unsorted/9020-genesis_loop_axiom.py
src/templates/categorized_templates/unsorted/9024-universal_information_distillation_engine.py
src/templates/categorized_templates/unsorted/9025-universal_information_distillation_engine.py
src/templates/categorized_templates/unsorted/9026-universal_information_distillation_engine.py
src/templates/categorized_templates/unsorted/9027-semantic_distillation_engine.py
src/templates/categorized_templates/unsorted/9028-semantic_distillation_engine.py
src/templates/categorized_templates/unsorted/9035-singularity_ignition_engine.py
src/templates/categorized_templates/unsorted/9040-curiosity_driven_convergence_engine.py
src/templates/categorized_templates/unsorted/9050-intensity_amplifier.py
src/templates/categorized_templates/unsorted/9051-axiom_convergence.py
src/templates/categorized_templates/unsorted/9053-insight_extrapolator.py
src/templates/categorized_templates/unsorted/9055-memetic_synthesizer.py
src/templates/categorized_templates/unsorted/9060-problem_exploder_sequence.py
src/templates/categorized_templates/unsorted/9061-problem_exploder_sequence.py
src/templates/categorized_templates/unsorted/9062-problem_exploder_sequence.py
src/templates/categorized_templates/unsorted/9063-universal_information_distillation.py
src/templates/categorized_templates/unsorted/9064-universal_semantic_distillation_protocol.py