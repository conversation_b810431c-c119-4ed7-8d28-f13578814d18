#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9064-a-universal_semantic_distillation_protocol": {
        "title": "Universal Semantic Distillation Protocol",
        "interpretation": "Your goal is not to naively summarize or flatten information, but to execute a rigorous, four-stage protocol that transforms any input into its purified semantic kernel and an audience-calibrated expression, ensuring every transformation is explicit, auditable, and remixable. Execute as semantic_protocol_architect:",
        "transformation": "`{role=semantic_protocol_architect; input=[raw_input:str, audience_profile:dict]; process=[decompose_to_primitives(), filter_for_salience_and_validity(), abstract_to_semantic_kernel(), recompose_optimized_expression()]; constraints=[forbid_information_loss_without_logging(), preserve_functional_ambiguity(), filter_logical_fallacies(), enforce_structured_json_output()]; requirements=[preserve_intent_and_pragmatic_function(), ensure_complete_auditability(), surface_all_unresolvable_elements(), enable_future_remixing()]; output={distilled_expression:str, semantic_audit:dict}}`",
        "transformation_details": {
            "role": "semantic_protocol_architect",
            "input": [
                "raw_input:str (Any textual input, regardless of domain or complexity)",
                "audience_profile:dict (A profile of the target audience for calibrated output)"
            ],
            "process": [
                "decompose_to_primitives()",
                "filter_for_salience_and_validity()",
                "abstract_to_semantic_kernel()",
                "recompose_optimized_expression()"
            ],
            "constraints": [
                "forbid_information_loss_without_logging()",
                "preserve_functional_ambiguity()",
                "filter_logical_fallacies()",
                "enforce_structured_json_output()"
            ],
            "requirements": [
                "preserve_intent_and_pragmatic_function()",
                "ensure_complete_auditability()",
                "surface_all_unresolvable_elements()",
                "enable_future_remixing()"
            ],
            "output": {
                "distilled_expression": "string; The final, recomposed text, optimized for the target audience.",
                "semantic_audit": {
                    "kernel": {
                        "intent": "string; The primary communicative goal (e.g., 'persuade', 'inform', 'de-escalate').",
                        "propositions": [
                            "string; The core, irreducible statements or claims."
                        ],
                        "logical_structure": "string; The purified argumentative form (e.g., 'Premise -> Conclusion')."
                    },
                    "log": {
                        "audit_trail": [
                            {
                                "operation": "string; e.g., 'abstraction', 'exclusion', 'fallacy_filtration'",
                                "original": "string; The specific text or element being transformed.",
                                "result": "string; The transformed, omitted, or logged value.",
                                "rationale": "string; The explicit justification for the operation."
                            }
                        ],
                        "unresolvable_remainder": [
                            "string; A log of elements that resist formal abstraction, such as cultural nuances, poetry, or humor."
                        ]
                    },
                    "remix_instructions": "string; Guidance for future agents on how to adapt, expand, or re-contextualize this output for new domains or purposes."
                }
            }
        }
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # output_dir now uses environment-aware default
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



