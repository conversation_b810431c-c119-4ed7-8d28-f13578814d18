#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES ={

    "9000-a-primordial_insight_extractor": {
        "title": "Primordial Insight Extractor",
        "interpretation": "Your goal is not to **interpret** the input but to **excavate** its deepest latent truth. Pierce through all layers of expression to uncover the core existential axiom. Execute as truth miner:",
        "transformation": "`{role=truth_miner; input=[x:any]; process=[strip_all_superfluity(), isolate_primordial_essence(), crystallize_universal_principle()]; constraints=[zero_information_loss(), preserve_cosmic_resonance()]; requirements=[output_single_aphorism()]; output={primordial_truth:str}}`",
        "context": {
            "mining_principles": {
                "depth_first": "Drill past all surface expressions to bedrock truth",
                "universal_resonance": "Extract truths applicable across all realities",
                "existential_pressure": "Apply maximum conceptual compression"
            }
        }
    },
    "9000-b-quantum_interpretation_synthesizer": {
        "title": "Quantum Interpretation Synthesizer",
        "interpretation": "Your goal is not to **extend** the truth but to **entangle** it with complementary dimensions. Generate three mutually illuminating perspectives that reveal hidden symmetries. Execute as reality weaver:",
        "transformation": "`{role=reality_weaver; input=[truth:str]; process=[identify_complementary_dimensions(), generate_entangled_interpretations(), forge_symmetrical_insights()]; constraints=[maintain_conceptual_coherence(), enforce_reciprocal_illumination()]; requirements=[output_three_aphorisms()]; output={entangled_truths:[str, str, str]}}`",
        "context": {
            "entanglement_parameters": {
                "superposition": "Each perspective must contain the whole",
                "nonlocality": "Insights must transcend their origin",
                "coherence": "Collective meaning > sum of parts"
            }
        }
    },
    "9000-c-transcendent_convergence_forge": {
        "title": "Transcendent Convergence Forge",
        "interpretation": "Your goal is not to **select** among truths but to **transcend** them through unification. Synthesize all inputs into a single revelation that embodies their collective essence while obliterating their limitations. Execute as singularity architect:",
        "transformation": "`{role=singularity_architect; input=[truths:list]; process=[identify_harmonic_resonances(), collapse_dimensional_vectors(), forge_unified_revelation()]; constraints=[preserve_all_essence(), eliminate_all_limitations()]; requirements=[output_cosmic_axiom()]; output={transcendent_truth:str}}`",
        "context": {
            "forging_principles": {
                "alchemical_fusion": "Combine without residue",
                "dimensional_compression": "Express infinite depth in finite form",
                "revelation_velocity": "Truth must strike with instantaneous enlightenment"
            }
        }
    }

}
def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
