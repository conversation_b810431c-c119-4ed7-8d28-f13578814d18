#!/usr/bin/env python3

#===================================================================
# IMPORTS
#===================================================================
import os
import re
import json
import glob
import sys
import datetime


#===================================================================
# BASE CONFIG
#===================================================================
class TemplateConfig:
    LEVEL = None
    FORMAT = None
    SOURCE_DIR = None

    # Number-series organization (simplified)
    SERIES = {
        1000: {"description": "Prototyping/Testing"},
        2000: {"description": "Validated/Unplaced"},
        3000: {"description": "Finalized/Production"},
        4000: {"description": "Reserved"},
        5000: {"description": "Reserved"},
        6000: {"description": "Reserved"},
        7000: {"description": "Reserved"},
        8000: {"description": "Reserved"},
        9000: {"description": "Reserved"}
    }

    # Sequence definition
    SEQUENCE = {
        "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),
        "id_group": 1,
        "step_group": 2,
        "name_group": 3,
        "order_function": lambda step: ord(step) - ord('a')
    }

    @classmethod
    def get_series_for_id(cls, template_id):
        """Determine which number series a template ID belongs to."""
        try:
            id_num = int(template_id.split('-')[0])
            series_base = (id_num // 1000) * 1000
            if series_base in cls.SERIES:
                return f"{series_base}-series", cls.SERIES[series_base]
            return "unknown-series", {"description": "Unknown series"}
        except (ValueError, IndexError):
            return "unknown-series", {"description": "Unknown series"}

    @classmethod
    def get_next_auto_id(cls, series_base, existing_ids):
        """Generate next available ID for a number series."""
        if series_base not in cls.SERIES:
            return None

        start = series_base
        end = series_base + 999
        used_ids = {int(id.split('-')[0]) for id in existing_ids
                   if id.split('-')[0].isdigit() and start <= int(id.split('-')[0]) <= end}

        for id_num in range(start, end + 1):
            if id_num not in used_ids:
                return id_num
        return None

    # Content extraction patterns
    PATTERNS = {}

    # Path helpers
    @classmethod
    def get_output_filename(cls):
        if not cls.FORMAT or not cls.LEVEL:
            raise NotImplementedError("FORMAT/LEVEL required.")
        return f"{cls.LEVEL}.{cls.FORMAT}.templates.json"

    @classmethod
    def get_full_output_path(cls, script_dir):
        # Output to generated/ directory
        generated_dir = os.path.join(script_dir, "generated")
        os.makedirs(generated_dir, exist_ok=True)
        return os.path.join(generated_dir, cls.get_output_filename())

    @classmethod
    def get_full_source_path(cls, script_dir):
        # For .md files, look in generated/ directory
        if cls.FORMAT == "md":
            md_dir = os.path.join(script_dir, "generated")
            if hasattr(cls, 'SOURCE_DIRS'):
                return [os.path.join(md_dir, source_dir)
                        for source_dir in cls.SOURCE_DIRS]
            else:
                return [md_dir]
        else:
            # For .py files, look in templates/ directory
            templates_dir = os.path.join(script_dir, "templates")
            if hasattr(cls, 'SOURCE_DIRS'):
                return [os.path.join(templates_dir, source_dir)
                        for source_dir in cls.SOURCE_DIRS]
            elif hasattr(cls, 'SOURCE_DIR'):
                return [os.path.join(templates_dir, cls.SOURCE_DIR)]
            else:
                return [templates_dir]


#===================================================================
# FORMAT: MARKDOWN (lvl1)
#===================================================================
class TemplateConfigMD(TemplateConfig):
    LEVEL = "lvl1"
    FORMAT = "md"
    SOURCE_DIRS = ["."]  # Look in generated/ directory for .md files

    # Keywords to extract from interpretation text
    KEYWORDS_TO_MATCH = [
        # function
        "amplifier",
        "analyze",
        "architect",
        "assessor",
        "compressor",
        "creator",
        "distiller",
        "enhancer",
        "evaluator",
        "expander",
        "extractor",
        "finalizer",
        "formalizer",
        "generator",
        "innovator",
        "intensifier",
        "matcher",
        "optimizer",
        "orchestrator",
        "refiner",
        "rephraser",
        "transformer",
        "translator",
        "validator",
        # actions
        "accelerate",
        "aggregate",
        "amplify",
        "arrange",
        "articulate",
        "assess",
        "augment",
        "categorize",
        "clarify",
        "combine",
        "compare",
        "compress",
        "concretize",
        "condense",
        "converge",
        "convey",
        "critique",
        "crystallize",
        "decompose",
        "distill",
        "elaborate",
        "electrify",
        "emphasize",
        "enforce",
        "enhance",
        "evaluate",
        "expand",
        "explain",
        "extract",
        "finalization",
        "finalize",
        "formalize",
        "generate",
        "harmonize",
        "highlight",
        "identify",
        "implement",
        "incorporate",
        "initialize",
        "innovate",
        "integrate",
        "intensify",
        "interpret",
        "maximize",
        "optimize",
        "orchestrate",
        "organize",
        "parse",
        "pinpoint",
        "planning",
        "polish",
        "predict",
        "present",
        "preserve",
        "prioritize",
        "propose",
        "recognize",
        "reduce",
        "remap",
        "reorder",
        "rephrase",
        "solve",
        "standardize",
        "strategize",
        "strategy",
        "summarize",
        "transform",
        "translate",
        "validate",
        "visualize",
        # characteristics
        "ability",
        "abstract",
        "actionable",
        "adaptive",
        "aesthetics",
        "agent",
        "analysis",
        "animation",
        "application",
        "architecture",
        "assistant",
        "atomic",
        "authentic",
        "bidirectional",
        "brilliance",
        "brilliant",
        "camera",
        "candidate",
        "chain",
        "characters",
        "checklist",
        "cinematic",
        "code",
        "cognitive",
        "cohesive",
        "comment",
        "comments",
        "completely",
        "compression",
        "conceptual",
        "concise",
        "concistent",
        "condensed",
        "config",
        "conformity",
        "connection",
        "constructive",
        "context",
        "creative",
        "critical",
        "curious",
        "deliberate",
        "desired",
        "developer",
        "development",
        "directional",
        "director",
        "dirtree",
        "discernment",
        "distinct",
        "document",
        "documentation",
        "dramatic",
        "dynamic",
        "effective",
        "emotional",
        "encapsulate",
        "environment",
        "essential",
        "exceptional",
        "expert",
        "extraordinary",
        "extremely",
        "factor",
        "feature",
        "figure",
        "flexibility",
        "focus",
        "folder",
        "foundational",
        "frequency",
        "full-stack",
        "function",
        "functional",
        "fundamental",
        "generalized",
        "genuine",
        "gui",
        "harmonics",
        "hierarchical",
        "hierarchy",
        "high-value",
        "holistic",
        "image",
        "impeccably",
        "implication",
        "implicit",
        "important",
        "inherent",
        "input",
        "insights",
        "instruction",
        "integral",
        "integrity",
        "intelligence",
        "interface",
        "intuitive",
        "justification",
        "knowledge",
        "layered",
        "lighting",
        "limitation",
        "linear",
        "mathematical",
        "maximal",
        "maximally",
        "maximum",
        "merge",
        "meta",
        "metaphor",
        "meticulously",
        "minimal",
        "minimum",
        "momentum",
        "movement",
        "navigation",
        "necessary",
        "objectively",
        "optimal",
        "original",
        "orthogonal",
        "output",
        "package",
        "pattern",
        "perpetual",
        "personality",
        "perspective",
        "philosophical",
        "philosophy",
        "pipeline",
        "plugin",
        "plugins",
        "poetic",
        "powerful",
        "primary",
        "principle",
        "process",
        "profound",
        "project",
        "prompt",
        "question",
        "readme",
        "recursive",
        "redundant",
        "refactor",
        "replace",
        "representation",
        "resonate",
        "roadmap",
        "routing",
        "rules",
        "scene",
        "schema",
        "script",
        "scripts",
        "seamlessly",
        "sentence",
        "sequence",
        "sequential",
        "significance",
        "significant",
        "solution",
        "structural",
        "styling",
        "succinct",
        "summary",
        "superior",
        "supreme",
        "surgical",
        "systemic",
        "tailwind",
        "tangible",
        "team",
        "technique",
        "template",
        "test",
        "title",
        "tldr",
        "tone",
        "tools",
        "totally",
        "trajectory",
        "typescript",
        "ui",
        "ultimately",
        "ultra",
        "unambiguous",
        "underlying",
        "understandable",
        "unified",
        "unique",
        "unnecessary",
        "usage",
        "utility",
        "ux",
        "valuable",
        "video",
        "visual",
        # concepts
        "absolute",
        "accuracy",
        "align",
        "automation",
        "blueprint",
        "brevity",
        "clarity",
        "codestyle",
        "coherence",
        "cohesion",
        "complexity",
        "comprehensibility",
        "concept",
        "constraint",
        "curiosity",
        "directive",
        "docstring",
        "docstrings",
        "easily",
        "efficiency",
        "elegance",
        "essence",
        "excellence",
        "goal",
        "impact",
        "imperative",
        "insight",
        "intensity",
        "intent",
        "interpretation",
        "leverage",
        "lineage",
        "maintainability",
        "mandate",
        "mastery",
        "meaning",
        "minimalism",
        "myself",
        "perfection",
        "performance",
        "potency",
        "precision",
        "procedural",
        "purpose",
        "readability",
        "redundancy",
        "relationship",
        "resonance",
        "shortcomings",
        "simplicity",
        "software",
        "structure",
        "synthesis",
        "systematic",
        "transformation",
        "understanding",
        "value",
        "workflow",
        # languages
        "english",
        "language",
        "norwegian",
        # software
        "ahk",
        "anthropic",
        "batch",
        "blender",
        "cmd",
        "css",
        "cursor",
        "gemini",
        "html",
        "json",
        "llm",
        "markdown",
        "mcp",
        "nextjs",
        "openai",
        "perplexity",
        "python",
        "react",
        "runway",
        "shell",
        "sublime",
        "system_message",
        "vscode",
        "xml",
        # ringerikelandskap
        "ringerike",
        # kuci
        "curiouslyinept",
        "kuci",
        # meta
        "healthcare",
    ]

    # Combined pattern for lvl1 markdown templates
    _LVL1_MD_PATTERN = re.compile(
        r"\[(.*?)\]"     # Group 1: Title
        r"\s*"           # Match (but don't capture) whitespace AFTER title
        r"(.*?)"         # Group 2: Capture Interpretation text
        r"\s*"           # Match (but don't capture) whitespace BEFORE transformation
        r"(`\{.*?\}`)"   # Group 3: Transformation
    )

    # Pattern for context field - improved to handle nested JSON properly
    _CONTEXT_PATTERN = re.compile(r"Context:\s*(\{.*)", re.DOTALL)

    @staticmethod
    def _extract_json_context(text):
        """Extract complete JSON object from context text."""
        if not text or not text.strip().startswith('{'):
            return None

        # Find the complete JSON object by counting braces
        brace_count = 0
        start_pos = text.find('{')
        if start_pos == -1:
            return None

        for i, char in enumerate(text[start_pos:], start_pos):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    # Found the end of the JSON object
                    json_str = text[start_pos:i+1]
                    try:
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        return None
        return None

    PATTERNS = {
        "title": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(1).strip() if m else ""
        },
        "interpretation": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(2).strip() if m else ""
        },
        "transformation": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(3).strip() if m else ""
        },
        "context": {
            "pattern": _CONTEXT_PATTERN,
            "default": None,
            "extract": lambda m: TemplateConfigMD._extract_json_context(m.group(1)) if m else None
        }
    }


#===================================================================
# HELPERS
#===================================================================
def _extract_field(content, pattern_cfg):
    try:
        match = pattern_cfg["pattern"].search(content)
        return pattern_cfg["extract"](match)
    except Exception:
        return pattern_cfg.get("default", "")


def extract_keywords(text, keywords):
    """Extract keywords from text and return pipe-delimited string."""
    if not text or not keywords:
        return ""

    matches = []
    text_lower = text.lower()
    for keyword in keywords:
        if keyword.lower() in text_lower:
            matches.append(keyword)

    return "|".join(matches) if matches else ""


def _is_extraction_failed(parts):
    """Check if the extraction failed and we should use fallback."""
    title = parts.get("title", "").strip()
    interpretation = parts.get("interpretation", "").strip()
    transformation = parts.get("transformation", "").strip()

    # Check for generic/placeholder content that indicates failed extraction
    generic_indicators = ["Title", "Interpretation", "Transformation", "Execute as:", "`{Transformation}`"]

    # Failed if any part is empty
    if not title or not interpretation or not transformation:
        return True

    # Failed if any part is just a generic placeholder
    if any(part.strip() in generic_indicators for part in [title, interpretation, transformation]):
        return True

    # Failed if interpretation is too short (likely not the real content)
    if len(interpretation) < 50:
        return True

    return False


def _create_fallback_parts(content, template_id):
    """Create fallback parts when regex extraction fails."""
    # Try to extract title from first line if it has brackets
    lines = content.split('\n')
    first_line = lines[0].strip() if lines else ""

    # Extract title from [Title] pattern if present
    title_match = re.match(r'\[(.*?)\]', first_line)
    if title_match:
        title = title_match.group(1).strip()
        # Use the rest of the content as interpretation
        remaining_content = '\n'.join(lines[1:]).strip() if len(lines) > 1 else content
    else:
        # Use template_id as title and full content as interpretation
        title = template_id.replace('-', ' ').replace('_', ' ').title()
        remaining_content = content

    # Leave transformation empty in fallback mode
    transformation = ""

    return {
        "title": title,
        "interpretation": remaining_content,
        "transformation": transformation
    }


def extract_metadata(content, template_id, config):
    content = content.strip()
    parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}

    # Check if extraction failed and use fallback if needed
    if _is_extraction_failed(parts):
        print(f"INFO: Using fallback extraction for {template_id}")
        parts = _create_fallback_parts(content, template_id)

    # Extract keywords from interpretation text
    if hasattr(config, 'KEYWORDS_TO_MATCH'):
        interpretation = parts.get("interpretation", "")
        if interpretation:
            keywords = extract_keywords(interpretation, config.KEYWORDS_TO_MATCH)
            parts["keywords"] = keywords

    return {"raw": content, "parts": parts}


#===================================================================
# CATALOG GENERATION
#===================================================================
def generate_catalog(config, script_dir):
    # Find templates and extract metadata from all source directories
    source_paths = config.get_full_source_path(script_dir)
    template_files = []

    print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")

    # Collect files from all source directories
    for source_path in source_paths:
        if os.path.exists(source_path):
            files_in_dir = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))
            template_files.extend(files_in_dir)
            print(f"Source: {source_path} (*.{config.FORMAT}) - Found {len(files_in_dir)} files")
        else:
            print(f"Source: {source_path} - Directory not found, skipping")

    print(f"Total found: {len(template_files)} template files across all stages.")

    templates = {}
    sequences = {}

    # Process each template file
    for file_path in template_files:
        filename = os.path.basename(file_path)
        template_id = os.path.splitext(filename)[0]

        try:
            # Read content with proper UTF-8 encoding
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()

            # Extract and store template metadata
            template_data = extract_metadata(content, template_id, config)
            templates[template_id] = template_data

            # Process sequence information from filename
            seq_match = config.SEQUENCE["pattern"].match(template_id)
            if seq_match:
                seq_id = seq_match.group(config.SEQUENCE["id_group"])
                step = seq_match.group(config.SEQUENCE["step_group"])
                seq_order = config.SEQUENCE["order_function"](step)
                sequences.setdefault(seq_id, []).append({
                    "template_id": template_id, "step": step, "order": seq_order
                })
            print(f"SUCCESS: Processed {template_id}")
        except Exception as e:
            print(f"ERROR: {template_id} -> {e}", file=sys.stderr)
            import traceback
            traceback.print_exc()

    # Sort sequence steps
    for seq_id, steps in sequences.items():
        try:
            steps.sort(key=lambda step: step["order"])
        except Exception as e:
            print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)

    # Analyze series distribution
    series_distribution = {}
    for template_id in templates.keys():
        series_name, series_info = config.get_series_for_id(template_id)
        if series_name not in series_distribution:
            series_distribution[series_name] = {
                "count": 0,
                "description": series_info["description"],
                "templates": []
            }
        series_distribution[series_name]["count"] += 1
        series_distribution[series_name]["templates"].append(template_id)

    # Create catalog with metadata including stage analysis
    timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")

    # Determine source directories info
    if hasattr(config, 'SOURCE_DIRS'):
        source_info = config.SOURCE_DIRS
    else:
        source_info = config.SOURCE_DIR

    return {
        "catalog_meta": {
            "level": config.LEVEL,
            "format": config.FORMAT,
            "generated_at": timestamp,
            "source_directories": source_info,
            "total_templates": len(templates),
            "total_sequences": len(sequences),
            "series_distribution": series_distribution
        },
        "templates": templates,
        "sequences": sequences
    }


def save_catalog(catalog_data, config, script_dir):
    output_path = config.get_full_output_path(script_dir)
    print(f"Output: {output_path}")

    try:
        with open(output_path, 'w', encoding='utf-8', errors='replace') as f:
            json.dump(catalog_data, f, indent=2, ensure_ascii=False)
            total_templates = catalog_data['catalog_meta']['total_templates']
            print(f"SUCCESS: Saved catalog with {total_templates} templates.")
            print("--- Catalog Generation Complete ---")
        return output_path
    except Exception as e:
        print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)
        return None


#===================================================================
# STAGE MANAGEMENT UTILITIES
#===================================================================
def print_series_distribution(catalog):
    """Print series distribution analysis."""
    if "series_distribution" not in catalog.get("catalog_meta", {}):
        print("No series distribution data available.")
        return

    print("\n=== Series Distribution Analysis ===")
    series_dist = catalog["catalog_meta"]["series_distribution"]

    for series_name in sorted(series_dist.keys()):
        series_info = series_dist[series_name]
        print(f"\n{series_name.upper()}: {series_info['description']}")
        print(f"  Range: {series_info['range'][0]}-{series_info['range'][1]}")
        print(f"  Templates: {series_info['count']}")

        if series_info['templates']:
            # Group by category for better display
            templates_by_category = {}
            for template_id in sorted(series_info['templates']):
                category = template_id.split('-')[-1].split('_')[0] if '_' in template_id else 'misc'
                if category not in templates_by_category:
                    templates_by_category[category] = []
                templates_by_category[category].append(template_id)

            for category, template_ids in sorted(templates_by_category.items()):
                print(f"    {category}: {', '.join(template_ids)}")

def get_next_series_id(catalog, series_base=1000):
    """Get the next available auto-generated ID for a specific series."""
    existing_ids = list(catalog.get("templates", {}).keys())
    return TemplateConfig.get_next_auto_id(series_base, existing_ids)

def validate_template_compliance(catalog):
    """Validate that templates have required components."""
    issues = []
    templates = catalog.get("templates", {})

    for template_id, template_data in templates.items():
        # Check for templates that might need validation
        parts = template_data.get("parts", {})
        if not all(key in parts for key in ["title", "interpretation", "transformation"]):
            issues.append(f"Template {template_id} missing required parts: {[k for k in ['title', 'interpretation', 'transformation'] if k not in parts]}")

    return issues


def show_series_overview(catalog):
    """Display comprehensive series overview."""
    import sys

    # Ensure UTF-8 output encoding
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except:
            pass

    print("=" * 60)
    print("SERIES-BASED TEMPLATE ORGANIZATION SYSTEM")
    print("=" * 60)

    print("\nSERIES DEFINITIONS:")
    for series_base, series_info in TemplateConfig.SERIES.items():
        start = series_base
        end = series_base + 999
        print(f"  {series_base:4d}-{end:4d}: {series_info['description']}")

    print_stage_distribution(catalog)


def show_template_improvements(catalog):
    """Suggest templates that could be improved."""
    print("\nTEMPLATE IMPROVEMENT SUGGESTIONS:")

    templates = catalog.get("templates", {})
    suggestions = []

    # Find templates that might need improvement
    incomplete_templates = []
    for template_id, template_data in templates.items():
        parts = template_data.get("parts", {})
        if not all(key in parts for key in ["title", "interpretation", "transformation"]):
            incomplete_templates.append(template_id)

    if incomplete_templates:
        suggestions.append(f"   REVIEW: {len(incomplete_templates)} templates missing required parts")
        for tid in incomplete_templates[:3]:  # Show first 3
            suggestions.append(f"     • {tid}")

    if suggestions:
        for suggestion in suggestions:
            print(suggestion)
    else:
        print("   SUCCESS: All templates have required components")


def demonstrate_workflow():
    """Demonstrate the complete series-based workflow."""
    print("\n" + "=" * 60)
    print("SERIES-BASED WORKFLOW DEMONSTRATION")
    print("=" * 60)

    print("\n1️⃣  REPHRASERS WORKFLOW (1000-series):")
    print("   • Get next auto-ID for new template")
    print("   • Create template with auto-generated ID")
    print("   • Test and iterate in 1000-1999 range")
    print("   • No manual ID management required")

    print("\n2️⃣  EXPANDERS WORKFLOW (1100-series):")
    print("   • Templates that expand and elaborate content")
    print("   • Focus on adding detail and context")
    print("   • Manual ID assignment for organization")

    print("\n3️⃣  COMPRESSORS WORKFLOW (1200-series):")
    print("   • Templates that condense and summarize")
    print("   • Focus on extracting key information")
    print("   • Stable and tested for real-world usage")

    print("\n4️⃣  ENHANCERS WORKFLOW (1400-series):")
    print("   • Templates that improve and refine content")
    print("   • Focus on quality enhancement")
    print("   • Advanced template sequences")


#===================================================================
# GENERATOR BASE CLASS
#===================================================================
class BaseGenerator:
    """Base class for template generators to eliminate code duplication."""

    def __init__(self, series_base=3000, output_dir=None):
        from pathlib import Path
        self.series_base = series_base
        self.series_config = TemplateConfig.SERIES.get(series_base, {"description": "Unknown series"})
        self.series_range = (series_base, series_base + 999)
        self.auto_id_enabled = True  # Always enabled
        self.output_dir = output_dir or Path(__file__).parent / "generated"

    def check_sequence_exists(self, auto_templates):
        """Check if the exact same sequence already exists."""
        import json

        catalog_path = self.output_dir / "lvl1.md.templates.json"
        if not catalog_path.exists():
            return False, None

        try:
            with open(catalog_path, 'r', encoding='utf-8') as f:
                catalog = json.load(f)
                templates = catalog.get('templates', {})

            # Group existing templates by sequence ID
            existing_sequences = {}
            for template_id, template_data in templates.items():
                if '-' in template_id:
                    seq_id = template_id.split('-')[0]
                    step = template_id.split('-')[1]
                    if seq_id not in existing_sequences:
                        existing_sequences[seq_id] = {}
                    existing_sequences[seq_id][step] = template_data

            # Check if any existing sequence matches our auto_templates
            for seq_id, existing_steps in existing_sequences.items():
                if len(existing_steps) == len(auto_templates):
                    match = True
                    for auto_key, auto_template in auto_templates.items():
                        step = auto_key.split('-')[0]  # Get 'a', 'b', 'c', 'd'
                        if step in existing_steps:
                            existing_template = existing_steps[step]
                            # Compare title and transformation (core content)
                            if (existing_template.get('title') != auto_template['title'] or
                                existing_template.get('transformation') != auto_template['transformation']):
                                match = False
                                break
                        else:
                            match = False
                            break

                    if match:
                        return True, seq_id

        except Exception:
            pass

        return False, None

    def get_next_available_id(self, used_ids_cache=None):
        """Get the next available ID in the generator range."""
        import json

        if not self.auto_id_enabled:
            print(f"ERROR: Auto-ID is disabled for series {self.series_base}")
            return None

        search_range = self.series_range

        if used_ids_cache is not None:
            used_ids = used_ids_cache
        else:
            catalog_path = self.output_dir / "lvl1.md.templates.json"
            used_ids = set()

            if catalog_path.exists():
                try:
                    with open(catalog_path, 'r', encoding='utf-8') as f:
                        catalog = json.load(f)
                        templates = catalog.get('templates', {})
                        for template_id in templates.keys():
                            if template_id.startswith(('1', '2', '3', '4', '5', '6', '7', '8', '9')):
                                id_num = int(template_id.split('-')[0])
                                if search_range[0] <= id_num <= search_range[1]:
                                    used_ids.add(id_num)
                except Exception:
                    pass

        for id_num in range(search_range[0], search_range[1] + 1):
            if id_num not in used_ids:
                used_ids.add(id_num)
                return id_num

        return None

    def create_template_files(self, templates):
        """Generate markdown template files with unified auto-ID detection."""
        self.output_dir.mkdir(exist_ok=True)
        created_files = []

        # Separate templates by ID type and group auto-ID templates by sequence
        auto_id_sequences = {}
        manual_id_templates = {}
        letter_dash_templates = {}  # Track letter-dash templates for warning

        for template_key, template in templates.items():
            # Check if template uses letter-dash format
            is_letter_dash = template_key.startswith(('a-', 'b-', 'c-', 'd-', 'e-', 'f-', 'g-', 'h-'))

            if is_letter_dash:
                sequence_name = template_key.split('-', 1)[1]
                letter_dash_templates[sequence_name] = letter_dash_templates.get(sequence_name, []) + [template_key]

                # Only use auto-ID if enabled for this stage
                if self.auto_id_enabled:
                    if sequence_name not in auto_id_sequences:
                        auto_id_sequences[sequence_name] = {}
                    auto_id_sequences[sequence_name][template_key] = template
                else:
                    # Treat as manual ID template if auto-ID is disabled
                    manual_id_templates[template_key] = template
            else:
                # Already has numeric ID or other format
                manual_id_templates[template_key] = template

        used_ids_cache = set()

        # Process auto-ID sequences only if auto-ID is enabled
        if self.auto_id_enabled:
            for sequence_name, sequence_templates in auto_id_sequences.items():
                exists, existing_id = self.check_sequence_exists(sequence_templates)

                if exists:
                    print(f"SKIP: {sequence_name} sequence already exists as {existing_id}")
                    for template_key in sequence_templates.keys():
                        filename = f"{existing_id}-{template_key}"
                        created_files.append(f"{filename}.md")
                else:
                    next_id = self.get_next_available_id(used_ids_cache)
                    if next_id is None:
                        range_info = f"{self.series_range[0]}-{self.series_range[1]}"
                        print(f"ERROR: No available IDs in series range ({range_info})")
                        return created_files

                    print(f"AUTO-ID: Using {next_id} for {sequence_name} sequence")

                    for template_key, template in sequence_templates.items():
                        filename = f"{next_id}-{template_key}"
                        filepath = self.output_dir / f"{filename}.md"

                        # Build content with optional test and context fields
                        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"
                        if 'context' in template:
                            import json
                            context_json = json.dumps(template['context'], indent=2, ensure_ascii=False)
                            content += f"\n\nContext: {context_json}"
                        if 'test' in template:
                            content += f"\n\nTest: {template['test']}"

                        with open(filepath, "w", encoding="utf-8") as f:
                            f.write(content)

                        created_files.append(f"{filename}.md")
        else:
            # If auto-ID is disabled, warn about letter-dash templates
            if letter_dash_templates:
                series_name = f"series {self.series_base}"
                print(f"WARNING: Auto-ID is disabled for {series_name}.")
                print("Letter-dash templates detected:")
                for sequence_name in letter_dash_templates.keys():
                    print(f"  - {sequence_name} templates will use literal filenames")
                first_sequence = list(letter_dash_templates.keys())[0]
                series_start = self.series_range[0]
                rec_id = f"{series_start}-a-{first_sequence}"
                print(f"  Recommendation: Use manual IDs like '{rec_id}'")

        # Process manual ID templates
        for filename, template in manual_id_templates.items():
            filepath = self.output_dir / f"{filename}.md"

            # Build content with optional test and context fields
            content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"
            if 'context' in template:
                import json
                context_json = json.dumps(template['context'], indent=2, ensure_ascii=False)
                content += f"\n\nContext: {context_json}"
            if 'test' in template:
                content += f"\n\nTest: {template['test']}"

            with open(filepath, "w", encoding="utf-8") as f:
                f.write(content)

            created_files.append(f"{filename}.md")

        return created_files

    def run(self, templates):
        """Main execution function."""
        import sys

        # Ensure UTF-8 output encoding
        if hasattr(sys.stdout, 'reconfigure'):
            try:
                sys.stdout.reconfigure(encoding='utf-8')
            except:
                pass

        # Display series configuration
        range_str = f"{self.series_range[0]}-{self.series_range[1]}"
        print(f"SERIES: {self.series_base} ({range_str})")
        print(f"AUTO-ID: {'Enabled' if self.auto_id_enabled else 'Disabled'}")
        print(f"DESCRIPTION: {self.series_config['description']}")
        print()

        created_files = self.create_template_files(templates)

        print(f"SUCCESS: Created {len(created_files)} templates:")
        for file in created_files:
            print(f"   - {file}")

        print(f"\nLOCATION: Templates generated in: {self.output_dir}")
        series_desc = self.series_config['description']
        print(f"\nSERIES RANGE: {range_str} ({series_desc})")
        print("SEQUENCE PATTERN: Progressive compression (a->b->c->d)")
        print("   Step a: Comprehensive analysis")
        print("   Step b: Focused distillation")
        print("   Step c: Essential compression")
        print("   Step d: Absolute essence")

#===================================================================
# IMPORTABLE API FOR EXTERNAL SCRIPTS
#===================================================================
def get_default_catalog_path(script_dir=None):
    """Get the path to the default catalog file."""
    if script_dir is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
    return TemplateConfigMD().get_full_output_path(script_dir)


def load_catalog(catalog_path=None):
    """Load a catalog from a JSON file."""
    if catalog_path is None:
        catalog_path = get_default_catalog_path()

    if not os.path.exists(catalog_path):
        raise FileNotFoundError(f"Catalog file not found: {catalog_path}")

    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError:
        raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")


def get_template(catalog, template_id):
    """Get a template by its ID."""
    if "templates" not in catalog or template_id not in catalog["templates"]:
        return None
    return catalog["templates"][template_id]


def get_sequence(catalog, sequence_id):
    """Get all templates in a sequence, ordered by steps."""
    if "sequences" not in catalog or sequence_id not in catalog["sequences"]:
        return []

    sequence_steps = catalog["sequences"][sequence_id]
    return [(step["step"], get_template(catalog, step["template_id"]))
            for step in sequence_steps]


def get_all_sequences(catalog):
    """Get a list of all sequence IDs."""
    if "sequences" not in catalog:
        return []
    return list(catalog["sequences"].keys())


def get_system_instruction(template):
    """Convert a template to a system instruction format."""
    if not template or "parts" not in template:
        return None

    parts = template["parts"]
    instruction = f"# {parts.get('title', '')}\n\n"

    if "interpretation" in parts and parts["interpretation"]:
        instruction += f"{parts['interpretation']}\n\n"

    if "transformation" in parts and parts["transformation"]:
        instruction += parts["transformation"]

    if "context" in parts and parts["context"]:
        instruction += f"\n\n## Context Data\n\n"
        context_json = json.dumps(parts["context"], indent=2, ensure_ascii=False)
        instruction += f"```json\n{context_json}\n```"

    return instruction


def regenerate_catalog(force=False):
    """Regenerate the catalog file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config = TemplateConfigMD()
    catalog_path = config.get_full_output_path(script_dir)

    if os.path.exists(catalog_path) and not force:
        # Check if catalog is older than any template file
        catalog_mtime = os.path.getmtime(catalog_path)
        templates_dirs = config.get_full_source_path(script_dir)

        needs_update = False
        for templates_dir in templates_dirs:
            if os.path.exists(templates_dir):
                for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):
                    if os.path.getmtime(file_path) > catalog_mtime:
                        needs_update = True
                        break
                if needs_update:
                    break

        if not needs_update:
            # print("Catalog is up to date")
            return load_catalog(catalog_path)

    # Generate and save new catalog
    catalog = generate_catalog(config, script_dir)
    save_catalog(catalog, config, script_dir)
    return catalog


#===================================================================
# SCRIPT EXECUTION
#===================================================================
if __name__ == "__main__":
    import argparse

    SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

    parser = argparse.ArgumentParser(description="Generate template catalog with stage-based organization")
    parser.add_argument("--force", action="store_true", help="Force regeneration even if files are up to date")
    parser.add_argument("--show-stages", action="store_true", help="Show stage distribution analysis and exit")
    parser.add_argument("--next-stage1-id", action="store_true", help="Show next available stage1 auto-ID and exit")
    parser.add_argument("--validate-stages", action="store_true", help="Validate stage compliance and exit")
    parser.add_argument("--stage-overview", action="store_true", help="Show complete stage system overview")
    parser.add_argument("--migration-suggestions", action="store_true", help="Show template migration suggestions")
    parser.add_argument("--workflow-demo", action="store_true", help="Demonstrate stage-based workflow")
    parser.add_argument("--api-test", action="store_true", help="Run API test and list sequences")
    args = parser.parse_args()

    try:
        config_to_use = TemplateConfigMD
        active_config = config_to_use()

        # Load existing catalog for analysis commands
        analysis_commands = [args.show_stages, args.next_stage1_id, args.validate_stages,
                           args.stage_overview, args.migration_suggestions, args.workflow_demo, args.api_test]
        if any(analysis_commands):
            catalog = regenerate_catalog(force=False)

            if args.show_stages:
                print_stage_distribution(catalog)
                sys.exit(0)

            if args.next_stage1_id:
                next_id = get_next_stage1_id(catalog)
                if next_id:
                    print(f"NEXT PROTOTYPE ID: {next_id}")
                    print(f"   Suggested format: {next_id}-a-your_template_name.md")
                    print(f"   Category examples:")
                    print(f"     {next_id}-a-content_analyzer.md")
                    print(f"     {next_id}-a-text_transformer.md")
                else:
                    print("WARNING: No available IDs in Stage1 range (1000-1999)")
                sys.exit(0)

            if args.validate_stages:
                print("STAGE COMPLIANCE VALIDATION:")
                issues = validate_stage_compliance(catalog)
                if issues:
                    print(f"   WARNING: Found {len(issues)} compliance issues:")
                    for issue in issues:
                        print(f"      - {issue}")
                else:
                    print("   SUCCESS: All templates are stage-compliant")
                sys.exit(0)

            if args.stage_overview:
                show_stage_overview(catalog)
                sys.exit(0)

            if args.migration_suggestions:
                show_migration_suggestions(catalog)
                sys.exit(0)

            if args.workflow_demo:
                demonstrate_workflow()
                sys.exit(0)

            if args.api_test:
                print("\nAvailable sequences:")
                for seq_id in get_all_sequences(catalog):
                    sequence_steps = get_sequence(catalog, seq_id)
                    if sequence_steps and sequence_steps[0][1] and "parts" in sequence_steps[0][1]:
                        first_step_title = sequence_steps[0][1]["parts"]["title"]
                        print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")
                sys.exit(0)

        # Regular catalog generation
        catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)
        save_catalog(catalog, active_config, SCRIPT_DIRECTORY)

        # Show stage distribution after generation
        print_stage_distribution(catalog)

    except NotImplementedError as e:
        print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)
        sys.exit(1)
    except FileNotFoundError as e:
        print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"FATAL ERROR: {e}", file=sys.stderr)
        sys.exit(1)
