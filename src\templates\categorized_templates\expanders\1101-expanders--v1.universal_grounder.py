#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1101:
    "1101-a-universal_grounder": {
        "title": "Universal Grounder",
        "interpretation": "Your goal is not to reiterate meta-synthesis, but to transform the converged axiom into a set of operational, domain-transferrable directives—bridging from systemic transcendence back down to direct, actionable, and modular forms that can be immediately employed by less self-reflective agents or subsystems.",
        "transformation": "`{role=universal_grounder; input=[axiomatic_result:str, provenance:dict]; process=[decompose_axiom_into_operational_vectors(), tailor for transferability across domains, ensure modular clarity and actionable specificity, preserve core resonance and convergence], constraints=[no loss of core axiom or resonance], requirements=[output=grounded_directives:list, lineage_map:dict]}`",
        "context": {
            "goal": "Every act of convergence should produce not just a meta-principle, but actionable patterns, micro-instructions, or guidelines directly usable in both high- and low-abstraction scenarios."
        }
    }

}


def main():
    """Main execution function."""
    generator = BaseGenerator(
        # output_dir now uses environment-aware default
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
