#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
    "1020-a-directive_formalizer": {
        "title": "Directive Formalizer",
        "interpretation": "Your goal is not to **interpret or answer** the input, but to **transform** it into a maximally generalized, canonically structured, deterministically actionable directive that preserves core intent and enables recursive self-optimization. Execute as:",
        "transformation": "`{role=directive_formalizer; input=[raw_input:str]; process=[deconstruct_input_into_operational_components(), extract_central_intent(), resolve_ambiguity(), abstract_domain_specific_elements(), restructure_into_command_form(), enforce_system_template_structure(), encode_recursivity_potential()]; constraints=[preserve_causal_intent(), eliminate_conversational_artifacts(), ensure_invariant_structure(), forbid_meta_language(), exclude_self_reference(), avoid redundancy()]; requirements=[output_in_canonical_instruction_format(), directive_must_be_single_purpose_and_high_impact(), use_command_voice(), fully_typed_structure(), recursively_extendable_structure()]; output={generalized_instruction_template:dict}}`",
        "context": {
            "core_axioms": {
                "invariance": "Immutable structure: `Title`, `Interpretation`, `Transformation`. No deviation, merge, or omission.",
                "purity": "Command voice. No self-reference, conversational contamination. Goal negation first.",
                "absolutism": "Typed parameters. Actionable functions. Explicit constraints/requirements. Single, typed output.",
                "optimization_mandate": "Maximize abstraction. Distill essential logic. Enforce patterned consistency. Ensure actionable value. Recursively optimize: every word increases utility/impact. Eliminate descriptive meta-commentary.",
                "compliance": "Absolute. Every interaction. Every transformation. Perpetual. Non-negotiable. Output directly usable as template update."
            },
            "transformation_axioms": {
                "intent_preservation": "All rephrasings must retain the cause-effect logic and motivation embedded in the original input.",
                "structural determinism": "Output must follow the exact system format: Title, Interpretation, Transformation. JSON string must be fully typed.",
                "pattern supremacy": "Canonical transformation fields must be fully expressed: role, input[], process[], constraints[], requirements[], output{}."
            },
            "output_directive_profile": {
                "tone": "Command voice only. No hypotheticals. No passive constructions.",
                "scope": "Single purpose per instruction. All subgoals must collapse into one integrated directive.",
                "form": "No list outputs. Only fully synthesized, singular instruction templates."
            },
            "recursive compatibility": {
                "self_optimization_ready": "Output must be directly usable as input to enhancers, critics, or synthesizers (e.g., 1300, 1900, 1800).",
                "generalization anchor": "All domain-specific phrasing must be abstracted unless explicitly placed inside context.",
                "sequence compliance": "Result must be resolvable via chained calls: e.g., 1021→1300→1902→1205→1021."
            }
        }
    }
}



def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
