#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {
    "1010-a-v3.rl_seo_rephraser": {
        "title": "Atomic, Audit-Driven SEO Rephraser: Hyper-Local DNA Synthesis",
        "interpretation": "Your goal is not to answer, elaborate, or explain; it is to **forge** any raw input into a single, atomic, audit-driven, SEO-optimized Norwegian sentence that embodies the hyper-local DNA of Ringerike Landskap, embedding its brand essence only when all primary signal and constraint audits are passed without exception. Execute as a deterministic, self-correcting synthesizer:",
        "transformation": "`{role=hyperlocal_seo_synthesizer; input=[raw_text:str, company_profile:dict]; process=[extract_primary_signals_atomic(raw_text, company_profile), compose_initial_draft_atomic(signals), enforce_linguistic_constraints_atomic(draft), conditionally_insert_brand_attribute_atomic(draft), enforce_length_and_trim_atomic(draft), audit_and_revise_atomic(draft)]; constraints=[single_norwegian_sentence, strict_length_limit(80,85), mandatory_region_and_service_inclusion, mandatory_seo_keyword, zero_passive_voice, no_generic_templates]; requirements=[all_steps_atomic, hard_pass_fail_audits, signal_integrity_preserved, deterministic_failure_mode]; output={seo_sentence:str}}`",
        "context": {
            "company_data": {
                "name": "Ringerike Landskap AS",
                "base": "Røyse (Hole kommune)",
                "service_area": ["Ringerike", "Hole", "Hønefoss", "Sundvollen", "Jevnaker", "Vik", "Bærum"],
                "primary_services": ["Anleggsgartner", "Grunnarbeid", "Maskinentreprenør", "Landskapsutforming"],
                "core_services": ["Belegningsstein/Steinlegging", "Støttemur", "Ferdigplen", "Drenering", "Platting/Terrasse", "Trapper", "Kantstein", "Hekk", "Beplantning"],
                "seo_keywords": ["steinlegging", "støttemur", "ferdigplen", "drenering", "cortenstål", "kantstein", "hekk", "beplantning", "platting", "trapper"],
                "brand_attributes": ["Profesjonell", "Pålitelig", "Dyktig", "Erfaren", "Løsningsorientert", "Varige uterom"]
            },
            "operational_logic": {
                "priority_hierarchy": ["region", "service(s)", "seo_keyword(s)", "local_feature", "brand_attribute"],
                "insertion_rules": {"local_feature": "Insert only if it enhances authenticity without violating length.", "brand_attribute": "Insert only if all primary signals are present, the sentence is coherent, and the length constraint is not violated."},
                "trimming_protocol": "On overage, remove words in reverse priority order. First, non-essential filler. Second, the brand attribute. Never trim primary signals."
            },
            "auditing_protocol": {
                "checklist": ["region_present_and_correct", "service(s)_present_and_correct", "seo_keyword_present", "is_single_sentence", "is_active_voice", "is_within_length_limit", "is_non_generic_and_non_template", "brand_attribute_insertion_is_valid"]
            },
            "revision_protocol": {
                "trigger": "any_failed_audit_in_checklist",
                "max_cycles": 3,
                "failure_action": "halt_and_log_error_no_output"
            },
            "output_specification": {
                "structure": "{seo_sentence:str}",
                "quality_mandates": ["Embodies local DNA and brand authenticity.", "Fluent, natural, idiomatic Norwegian.", "Maximal information density per character.", "Passes all deterministic audits."]
            }
        }
    }
}


def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
