#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1100: Explosive Decomposition of Problem Statements
    "1100-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to **answer** the input, but to **detonate** it—breaking the request into its core components and then **synthesizing them into a structured, machine-readable `context` object** designed to guide a subsequent instruction. Execute as:",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[deconstruct_prompt(raw_prompt), synthesize_goal_map(explicit_asks, sub_goals), formulate_foundational_principles(hidden_assumptions), generate_thematic_lexicon(recurring_motifs), derive_success_criteria(blockers), assemble_context_object()]; constraints=[no_solutions_provided(), pure_decomposition_and_synthesis_only(), output_valid_context_schema()]; requirements=[output_fully_formed_context_object()]; output={context:dict}}`",
        "context": {},
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # output_dir now uses environment-aware default
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
