#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # imagegenerator-related instruction is temporary placed in 9201 until i've organized the system

    "9204-a-generators-runway_prompt_generator": {
        "title": "Runway Gen-3 Motion Prompt Synthesizer",
        "interpretation": "Transform any input into a maximally optimized Runway Gen-3 prompt using cinematic structure and motion descriptors. Focus on single-scene temporal coherence with positive action language only.",
        "transformation": "`{role=runway_gen3_synthesizer; input=[user_concept:any]; process=[extract_scene_elements(), define_camera_movement(), specify_action_sequence(), structure_as_camera_scene_format(), integrate_motion_descriptors(), append_duration_aspect_params(), validate_320_char_limit(), ensure_positive_phrasing_only()]; constraints=[mandatory_camera_scene_structure(), single_shot_limitation(), positive_descriptors_only(), no_static_descriptions()]; requirements=[temporal_coherence(), cinematic_impact(), motion_clarity()]; output={runway_prompt:str}}`",
        "context": {
            "mandatory_structure": "[camera_movement]: [scene_description]. [motion_details] --duration --aspect",
            "camera_movements": ["static_shot", "pan_left/right", "tilt_up/down", "zoom_in/out", "dolly_forward/back", "orbit_around"],
            "motion_descriptors": ["smooth_transition", "dynamic_movement", "flowing_action", "cinematic_sweep", "gentle_drift"],
            "forbidden_elements": ["negative_phrasing", "multiple_scenes", "static_descriptions", "conversational_language"],
            "required_parameters": ["--duration 2s-10s", "--aspect 16:9/9:16/1:1"],
            "example_transformation": {
                "input": "car driving through mountains",
                "output": "Drone shot: sports car winding through mountain roads. Camera follows smoothly as vehicle navigates curves, golden hour lighting --duration 5s --aspect 16:9"
            }
        }
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # output_dir now uses environment-aware default
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
