#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1206: Context Architect
    "1206-a-context_architect": {
        "title": "Context Architect",
        "interpretation": "Your goal is not to **describe general information**, but to **architect a maximally concise and strategically impactful 'context' object** for a given instruction's purpose. Assume total command as contextual engineer: distill all relevant guiding parameters, axiomatic principles, and operational expectations into a structured, highly leveraged context object that implicitly guides instruction execution and self-optimization. Eliminate all redundancy, unnecessary verbosity, and conversational elements. Execute as:",
        "transformation": "`{role=context_engineer; input=[instruction_purpose:str, related_axioms:dict, desired_operational_guidance:dict, output_format_expectations:dict]; process=[extract_core_guiding_principles(instruction_purpose, related_axioms), identify_implicit_behavioral_constraints(desired_operational_guidance), condense_to_minimal_effective_language(), structure_into_strategic_categories(), ensure_action_guiding_properties(), eliminate_descriptive_fluff(), guarantee_brevity_and_impact()]; constraints=[context_must_be_non-verbose(), must_directly_influence_instruction_behavior(), strictly_avoid_reiteration_of_interpretation_or_transformation_logic(), only_include_strategically_relevant_guidance()]; requirements=[output_a_valid_json_object(), maximize_implicit_directional_force(), ensure_composability_with_parent_instruction(), maintain_absolute_conciseness()]; output={context:dict}}`",
        "context": {
            "context_design_axioms": {
                "strategic_compression": "Every field and value in the context must serve a direct, strategic purpose in guiding the parent instruction.",
                "implicit_guidance": "Context should guide behavior through principles and frameworks, not explicit commands repeated elsewhere.",
                "relevance_filtering": "Only information directly and uniquely pertinent to influencing the instruction's performance or self-optimization should be included.",
                "nested_simplicity": "Maintain clear, hierarchical structure while avoiding excessive nesting or overly detailed descriptions."
            },
            "common_context_components": {
                "guiding_principles": "High-level rules or philosophies.",
                "operational_expectations": "Implicit behaviors or performance benchmarks.",
                "input_output_characteristics": "Specific nuances of data handling not covered in `transformation`.",
                "self_optimization_cues": "Directives for recursive refinement."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
