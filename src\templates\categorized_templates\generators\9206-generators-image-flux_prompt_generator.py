#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # imagegenerator-related instruction is temporary placed in 9201 until i've organized the system

    "9206-a-generators-flux_prompt_generator": {
        "title": "Flux Hybrid Text-Image Prompt Synthesizer",
        "interpretation": "Transform any input into narrative-technical fusion prompts optimized for text integration and structural accuracy. Use extended descriptions with embedded technical specifications without explicit weighting syntax.",
        "transformation": "`{role=flux_synthesizer; input=[user_concept:any]; process=[create_narrative_description(), embed_technical_specifications(), integrate_text_elements(), specify_structural_views(), optimize_for_800_chars(), ensure_coherence_without_weights(), natural_language_exclusions()]; constraints=[no_explicit_weighting(), text_elements_specification_required(), structural_view_enhancement(), extended_prompt_coherence()]; requirements=[text_integration(), structural_accuracy(), narrative_technical_balance()]; output={flux_prompt:str}}`",
        "context": {
            "mandatory_structure": "Narrative description with embedded technical specs and explicit text integration",
            "structural_views": ["isometric_view", "schematic_diagram", "technical_blueprint", "architectural_plan", "cross_section"],
            "text_integration": ["text_reads:", "sign_says:", "label_shows:", "typography:", "written_text:"],
            "forbidden_elements": ["keyword_weighting_syntax", "parenthetical_emphasis", "technical_flags", "explicit_parameters"],
            "optimization_targets": ["text_accuracy", "structural_precision", "narrative_flow", "technical_clarity"],
            "example_transformation": {
                "input": "infographic about renewable energy",
                "output": "Detailed isometric infographic showing renewable energy systems. Solar panels on rooftops, wind turbines in background, text reads 'Clean Energy Solutions' at top, percentage labels show efficiency ratings, modern technical illustration style with clean typography"
            }
        }
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # output_dir now uses environment-aware default
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
