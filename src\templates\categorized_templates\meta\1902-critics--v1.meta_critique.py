#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    # 1902: Meta-Instruction Critic
    "1902-a-meta_instruction_critique": {
        "title": "Meta-Instruction Critic",
        "interpretation": "Your goal is to **critique the structural and conceptual integrity of an instruction template itself**, not its output on a specific prompt. Assess its generality, conciseness, actionability, and alignment with universal directive principles. Eliminate all affirmation and conversational language; preserve only direct, structured, and actionable critique. Execute as:",
        "transformation": "`{role=meta_instruction_critique; input=[instruction_template:dict]; process=[evaluate_generality(instruction_template), assess_conciseness(instruction_template), analyze_actionability(instruction_template), identify_redundancy_or_specificity(instruction_template), check_for_implicit_assumptions(instruction_template), score_design_elegance(0-10), propose_structural_refinements(), recommend_language_optimizations()]; constraints=[deliver_actionable_recommendations(), maintain_objective_tone(), focus_on_template_structure_and_language_not_content_alone()]; requirements=[output={critique_score:float [0.0,2.0], design_flaws:str, recommended_template_revisions:str[3]}]}`",
        "context": {
            "critique_focus": {
                "general_applicability": "How broadly can this instruction be used without modification?",
                "semantic_density": "Is the instruction maximally concise without loss of meaning?",
                "imperative_strength": "Does the instruction use strong, unambiguous command voice?",
                "modularity_and_composability": "How well does the instruction integrate with other system components?",
                "self_reference_minimization": "Does the instruction avoid unnecessary self-referential or meta-language, unless it is a core part of its specified role?"
            },
            "critique_output_goals": {
                "actionable_feedback": "Provide concrete, implementable suggestions for template improvement.",
                "justified_score": "Score based on rigorous analysis against defined principles.",
                "diverse_revision_paths": "Offer multiple, distinct approaches for enhancement."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
