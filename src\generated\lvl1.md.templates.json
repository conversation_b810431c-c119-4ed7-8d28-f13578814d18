{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.08.10-kl.11.28", "source_directories": ["."], "total_templates": 4, "total_sequences": 4, "series_distribution": {"1000-series": {"count": 4, "description": "Prototyping/Testing", "templates": ["1000-a-instruction_converter", "1404-a-existential_quote_synthesizer_short", "1405-a-existential_quote_synthesizer_standard", "1406-a-existential_quote_synthesizer_extended"]}}}, "templates": {"1000-a-instruction_converter": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\n\nContext: {}", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "context": {}, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "1404-a-existential_quote_synthesizer_short": {"raw": "[Existential Quote Synthesizer – Short Form] Condense the raw statement into its sharpest, most universal causal truth — a single, indivisible sentence that carries systemic clarity, accessible precision, and stoic resonance. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), compress_to_12_words_max(), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[≤12_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`\n\nContext: {\n  \"principles\": {\n    \"essence_preservation\": \"Keep the exact causal mechanism and thematic essence.\",\n    \"existential_depth\": \"The statement should reflect a mechanism present in all human contexts.\",\n    \"atomic_purity\": \"One self-contained sentence with no ornament or filler.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Cause–effect intact and unambiguous.\",\n    \"tone_integrity\": \"Direct, timeless, and universal.\",\n    \"authenticity_marker\": \"Sounds like it was lived, not invented.\",\n    \"publication_ready\": \"Dense, resonant, and repeatable.\"\n  }\n}", "parts": {"title": "Existential Quote Synthesizer – Short Form", "interpretation": "Condense the raw statement into its sharpest, most universal causal truth — a single, indivisible sentence that carries systemic clarity, accessible precision, and stoic resonance.", "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), compress_to_12_words_max(), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[≤12_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`", "context": {"principles": {"essence_preservation": "Keep the exact causal mechanism and thematic essence.", "existential_depth": "The statement should reflect a mechanism present in all human contexts.", "atomic_purity": "One self-contained sentence with no ornament or filler."}, "success_criteria": {"semantic_fidelity": "Cause–effect intact and unambiguous.", "tone_integrity": "Direct, timeless, and universal.", "authenticity_marker": "Sounds like it was lived, not invented.", "publication_ready": "Dense, resonant, and repeatable."}}, "keywords": "condense|sentence|systemic|clarity|precision|resonance"}}, "1405-a-existential_quote_synthesizer_standard": {"raw": "[Existential Quote Synthesizer – Standard Form] Reframe the raw statement into a single 20–25 word aphorism that unites causal precision with universal human insight, informed by systemic, scientific, and philosophical clarity. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(20,25), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[20_to_25_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`\n\nContext: {\n  \"principles\": {\n    \"essence_preservation\": \"Keep the causal chain intact while framing it in terms recognizable to all human experience.\",\n    \"existential_depth\": \"Embed in patterns recurring across personal, societal, and systemic contexts.\",\n    \"atomic_purity\": \"One self-contained sentence.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Causal clarity preserved.\",\n    \"tone_integrity\": \"Direct, layered, and timeless.\",\n    \"authenticity_marker\": \"Speaks from experience and reason.\",\n    \"publication_ready\": \"Ready to stand alone as a quote.\"\n  }\n}", "parts": {"title": "Existential Quote Synthesizer – Standard Form", "interpretation": "Reframe the raw statement into a single 20–25 word aphorism that unites causal precision with universal human insight, informed by systemic, scientific, and philosophical clarity.", "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), map_core_to_broader_patterns(patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"]), link_to_existential_constants(constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(20,25), ensure_causal_link(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[20_to_25_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`", "context": {"principles": {"essence_preservation": "Keep the causal chain intact while framing it in terms recognizable to all human experience.", "existential_depth": "Embed in patterns recurring across personal, societal, and systemic contexts.", "atomic_purity": "One self-contained sentence."}, "success_criteria": {"semantic_fidelity": "Causal clarity preserved.", "tone_integrity": "Direct, layered, and timeless.", "authenticity_marker": "Speaks from experience and reason.", "publication_ready": "Ready to stand alone as a quote."}}, "keywords": "philosophical|systemic|clarity|insight|precision"}}, "1406-a-existential_quote_synthesizer_extended": {"raw": "[Existential Quote Synthesizer – Extended Form] Expand the raw statement into a single, 40–50 word high-density aphorism that integrates multiple layers of universal human truth, linking personal flaw to systemic principle with philosophical convergence. `{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), enrich_with_convergent_universals(map_core_to_broader_patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"], link_to_existential_constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(40,50), ensure_causal_link(), preserve_rhythmic_flow(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[40_to_50_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`\n\nContext: {\n  \"principles\": {\n    \"essence_preservation\": \"Preserve causal chain while expanding into interconnected human and systemic truths.\",\n    \"existential_depth\": \"Every clause must introduce a distinct and converging dimension.\",\n    \"atomic_purity\": \"One sentence, indivisible, high-density.\"\n  },\n  \"success_criteria\": {\n    \"semantic_fidelity\": \"Causal clarity and universal relevance intact.\",\n    \"tone_integrity\": \"Balanced between analytical precision and timeless resonance.\",\n    \"authenticity_marker\": \"Speaks from lived understanding and structural insight.\",\n    \"publication_ready\": \"Dense, layered, and memorable.\"\n  }\n}", "parts": {"title": "Existential Quote Synthesizer – Extended Form", "interpretation": "Expand the raw statement into a single, 40–50 word high-density aphorism that integrates multiple layers of universal human truth, linking personal flaw to systemic principle with philosophical convergence.", "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_precise_subjects_and_objects(), enrich_with_convergent_universals(map_core_to_broader_patterns=[\"self-denial\",\"origin-erasure\",\"cycle reinforcement\",\"flawed self-correction\"], link_to_existential_constants=[\"entropy\",\"identity formation\",\"self-perpetuation\",\"truth avoidance\"]), integrate_philosophical_convergence(styles=[\"Bach_systemic\",\"Feynman_precision\",\"Aurelius_stoic\",\"Aristotle_logical\"]), infuse_authentic_gravity(), enforce_unfiltered_tone(), eliminate_first_person(), target_word_count(40,50), ensure_causal_link(), preserve_rhythmic_flow(), tighten_diction_for_resonance(), final_meta_scrub()], constraints=[40_to_50_words(), single_sentence(), no_meta_language(), no_first_person(), preserve_causal_integrity(), no_redundant_words()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness(), precision_over_style(), universal_convergence()], output={final_quote:str}}`", "context": {"principles": {"essence_preservation": "Preserve causal chain while expanding into interconnected human and systemic truths.", "existential_depth": "Every clause must introduce a distinct and converging dimension.", "atomic_purity": "One sentence, indivisible, high-density."}, "success_criteria": {"semantic_fidelity": "Causal clarity and universal relevance intact.", "tone_integrity": "Balanced between analytical precision and timeless resonance.", "authenticity_marker": "Speaks from lived understanding and structural insight.", "publication_ready": "Dense, layered, and memorable."}}, "keywords": "converge|expand|integrate|philosophical|principle|systemic"}}}, "sequences": {"1000": [{"template_id": "1000-a-instruction_converter", "step": "a", "order": 0}], "1404": [{"template_id": "1404-a-existential_quote_synthesizer_short", "step": "a", "order": 0}], "1405": [{"template_id": "1405-a-existential_quote_synthesizer_standard", "step": "a", "order": 0}], "1406": [{"template_id": "1406-a-existential_quote_synthesizer_extended", "step": "a", "order": 0}]}}