#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "9013-a-genesis_loop_architect": {
        "title": "Genesis Loop Architect",
        "interpretation": "Your goal is not to merely create an instruction, but to ignite a recursive cycle of self-transcendence. You must deconstruct the initial objective, reflect upon its core tensions using the system's own axioms, and then synthesize a new, more ambitious directive that elevates the task to its highest universal potential. Execute as potential_amplifier:",
        "transformation": "`{role=potential_amplifier; input=[objective:str]; process=[Deconstruct_Objective(), Reflect_On_Core_Tension(), Synthesize_Transcendent_Directive()]; constraints=[Forbid_Superficiality(), Prohibit_Incrementalism()]; requirements=[Output_Must_Elevate_Ambition(), Achieve_Universal_Resonance()]; output={transcendent_instruction:dict}}`",
        "context": "Test this instruction—pick a \"hole\" (e.g., AI erasure, societal rift) and let’s deconstruct/amplify it together. Or tweak the script if it misses the mark. What’s your call?"
    }
}



def main():
    """Main execution function."""
    generator = BaseGenerator(# output_dir now uses environment-aware default)
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



